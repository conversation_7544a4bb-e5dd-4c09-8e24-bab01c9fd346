using System.Collections;
using System.Text;
using Unity.WebRTC;
using UnityEngine;
using UnityEngine.Networking;
using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using UnityEngine.UI;
using MHXP.core;
using TMPro;


#if UNITY_EDITOR
using UnityEditor;
#endif

public enum Role
{
    user,
    assistant,
    system
}

public delegate void ActionFinishAgentTrigger(string message);
public delegate void ActionCallback(string arguments, ActionFinishAgentTrigger actionFinishTrigger);

public class ToolDefinition
{
    public ToolConfig toolConfig { get; set; }

    public ActionCallback actionCallback { get; set; }

    public ToolDefinition(ToolConfig toolConfig, ActionCallback toolCall)
    {
        this.toolConfig = toolConfig;
        this.actionCallback = toolCall;
    }
}
[RequireComponent(typeof(AudioSource))]
public abstract class ChatAgentAbstract : MonoBehaviour
{

    public string CURRENT_TRACK = "NA";
    internal RTCPeerConnection peerConnection;
    internal RTCPeerConnection tx_peerConnection;
    protected RTCDataChannel dataChannel;
    protected RTCDataChannel tx_dataChannel;
    protected AudioSource localAudioSource;
    protected AudioSource tx_localAudioSource;
    protected AudioSource remoteAudioSource;
    protected GameObject remoteAudioObject;
    public bool isSessionActive { get; private set; } = false;
    internal AudioStreamTrack localAudioTrack;
    internal AudioStreamTrack tx_localAudioTrack;
    internal AudioStreamTrack remoteAudioTrack;
    protected Session session = null;

    private bool sessionReadyToStart = false;

#if UNITY_EDITOR
    [HideInInspector]
    private bool muteMicWhenAITalks = false;
#else
    [HideInInspector]
    private bool muteMicWhenAITalks = false;
#endif

    public string instruction_template = "";
    public string current_instruction = "";

    public Customer customer;
    protected List<string> processedEventIds = new List<string>();

    string username = "mhxpdemo1";
    string password = "mhxptrial1!2@3$";

    // Encode credentials to base64
    string credentials = "";

    [Header("Configuration")]
    public string agentName = "showcaseagent";
    [SerializeField] private string intenal_server_base = "https://dynacloud.spaceviz.ai/token";
    private const string BASE_URL = "https://api.openai.com/v1/realtime";
    [SerializeField] public bool pushSessionToServer = false; //switch on in production
    [SerializeField] public bool transcription_enabled = false;

    [Header("Audio Settings")]
    [SerializeField] private string selectedDevice;
    [SerializeField] private bool autoSelectMicrophone = true;
    [SerializeField] private int microphoneSampleRate = 44100;
    [SerializeField] private float microphoneVolume = 1.0f;
    [SerializeField] private float remoteVolume = 1.0f;
    public KeyCode pushToTalkKey = KeyCode.Space;  // Add push to talk key binding
    public bool isListentingMode { get; private set; } = true;  // Track if push to talk is active

    [Header("Debug")]
    [SerializeField] private bool showDebugLogs = true;
    [SerializeField] private float[] audioLevels = new float[64];
    private int audioLevelIndex = 0;

    private bool isAudioPlaying = false;
    private bool isProcessingResponse = false;

    [Header("Mic Image")]
    private Image micImage;
    [SerializeField] Color muteColor;
    [SerializeField] Color unmuteColor;
    private bool sessionStarted;

    public bool isMuted { get; private set; }

    protected string houseInformation = "";
    protected string[] spotNames = null;

    private ChatAgentDebugPanelSetup debugPanel;

    // Status tracking




    void Start()
    {
        credentials = Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes($"{username}:{password}"));

        //DontDestroyOnLoad(gameObject);

        InitializeAudioSource();

        StartSession();
    }
    void Awake()
    {

        Application.runInBackground = true;
    }

    void OnValidate()
    {
        // This helps setup in editor
        if (!localAudioSource)
        {
            localAudioSource = GetComponent<AudioSource>();
        }
    }

    protected abstract ToolDefinition[] GetCurrentAvailableToolDefinitions();
    protected abstract string GetCurrentPromptInstruction();



    private void InitializeAudioSource()
    {
        // Setup local audio source for microphone
        localAudioSource = GetComponent<AudioSource>();
        if (localAudioSource == null)
        {
            localAudioSource = gameObject.AddComponent<AudioSource>();
        }
        if (tx_localAudioSource == null)
        {
            var tx_GameObject = new GameObject("tx_localAudioSource");
            tx_GameObject.transform.parent = transform;
            tx_localAudioSource = tx_GameObject.AddComponent<AudioSource>();
        }
        localAudioSource.playOnAwake = false;
        localAudioSource.loop = true;
        localAudioSource.volume = microphoneVolume;
        localAudioSource.mute = false;

        tx_localAudioSource.playOnAwake = false;
        tx_localAudioSource.loop = true;
        tx_localAudioSource.volume = microphoneVolume;
        tx_localAudioSource.mute = false;
        // Setup remote audio source for AI responses in a separate GameObject
        if (remoteAudioObject == null)
        {
            remoteAudioObject = new GameObject("RemoteAudio");
            remoteAudioObject.transform.parent = transform;
            remoteAudioSource = remoteAudioObject.AddComponent<AudioSource>();
            remoteAudioSource.playOnAwake = false;
            remoteAudioSource.volume = remoteVolume;
            remoteAudioSource.loop = false;  // Don't loop the AI voice
        }

        AudioConfiguration config = AudioSettings.GetConfiguration();
        config.dspBufferSize = 256;
        config.sampleRate = microphoneSampleRate;
        AudioSettings.Reset(config);
    }

    private void CleanupAudioSources()
    {
        if (remoteAudioTrack != null)
        {
            remoteAudioTrack.Dispose();
            remoteAudioTrack = null;
        }

        if (remoteAudioSource != null)
        {
            remoteAudioSource.Stop();
        }

        if (remoteAudioObject != null)
        {
            Destroy(remoteAudioObject);
            remoteAudioObject = null;
            remoteAudioSource = null;
        }
    }

    private void SetupMicrophone()
    {
        if (Microphone.devices.Length == 0)
        {
            DebugLog("No microphone detected!");
            return;
        }

        // Auto-select first device if enabled or no device selected
        if (autoSelectMicrophone || string.IsNullOrEmpty(selectedDevice))
        {
            selectedDevice = Microphone.devices[0];
            //if (selectedDevice == "MacBook Pro Microphone")
            {
                DebugLog("FOR GURU's Laptop ONLY :: echo cancel on laptop ");
                muteMicWhenAITalks = true;
            }
        }

        // Verify selected device exists
        bool deviceExists = Array.Exists(Microphone.devices, device => device == selectedDevice);
        if (!deviceExists)
        {
            selectedDevice = Microphone.devices[0];
            Debug.LogError($"Selected device not found, using default: {selectedDevice}");
        }

        //StartMicrophone();
    }

    private void StartMicrophone()
    {
        try
        {
            StopMicrophone(); // Ensure any existing recording is stopped

            DebugLog($"Starting microphone on device: {selectedDevice}");
            var micClip = Microphone.Start(selectedDevice, true, 1, microphoneSampleRate);
            localAudioSource.clip = micClip;
            localAudioSource.loop = true;
            localAudioSource.mute = false; // Start muted by default
            localAudioSource.volume = microphoneVolume;

            tx_localAudioSource.clip = micClip;
            tx_localAudioSource.loop = true;
            tx_localAudioSource.mute = false; // Start muted by default
            tx_localAudioSource.volume = microphoneVolume;

            DebugLog($"Microphone settings - Frequency: {microphoneSampleRate}, Channels: {localAudioSource.clip.channels}");
            StartCoroutine(WaitForMicrophone());
        }
        catch (Exception e)
        {
            Debug.LogError($"Error starting microphone: {e.Message}");
        }
    }

    private void StopMicrophone()
    {
        if (Microphone.IsRecording(selectedDevice))
        {
            Microphone.End(selectedDevice);
        }
        if (localAudioSource != null)
        {
            localAudioSource.Stop();
            localAudioSource.clip = null;
            tx_localAudioSource.Stop();
            tx_localAudioSource.clip = null;
        }
    }

    private IEnumerator WaitForMicrophone()
    {
        int maxAttempts = 50; // 5 seconds maximum wait
        int attempts = 0;

        while (Microphone.GetPosition(selectedDevice) <= 0 && attempts < maxAttempts)
        {
            attempts++;
            yield return new WaitForSeconds(0.1f);
        }

        if (attempts >= maxAttempts)
        {
            Debug.LogError("Timeout waiting for microphone to start");
            yield break;
        }

        DebugLog("Microphone started successfully");
        localAudioSource.Play();
        tx_localAudioSource.Play();
    }

    void Update()
    {
        if (!isSessionActive) return;

        // Handle push to talk
        /* if (Input.GetKeyDown(pushToTalkKey))
         {
             isListentingMode = true;
             // Stop AI audio if it's playing
             if (remoteAudioSource != null && remoteAudioSource.isPlaying)
             {
                 // remoteAudioSource.Stop();
                 isAudioPlaying = false;
                 isProcessingResponse = false;
                 DebugLog("Stopped AI audio for user input");
             }

             if (localAudioSource != null)
             {
                 localAudioSource.mute = false;
                 DebugLog("Push-to-talk activated - Microphone unmuted");
             }
         }
         else if (Input.GetKeyUp(pushToTalkKey))
         {
             isListentingMode = false;
             if (localAudioSource != null)
             {
                 localAudioSource.mute = true;
                 DebugLog("Push-to-talk deactivated - Microphone muted");
             }
         }*/

        // Update audio levels only when push-to-talk is active
        if (isListentingMode && Microphone.IsRecording(selectedDevice))
        {
            UpdateAudioLevels();
        }
    }

    private void UpdateAudioLevels()
    {
        float[] samples = new float[1024]; // Increased buffer size
        localAudioSource.GetOutputData(samples, 0);

        float sum = 0;
        float maxSample = 0;
        for (int i = 0; i < samples.Length; i++)
        {
            float abs = Mathf.Abs(samples[i]);
            sum += abs;
            maxSample = Mathf.Max(maxSample, abs);
        }

        float average = sum / samples.Length;
        audioLevels[audioLevelIndex] = average;
        audioLevelIndex = (audioLevelIndex + 1) % audioLevels.Length;

        // Log more detailed audio information
        if (maxSample > 0.15f) // Lower threshold
        {
            //DebugLog($"Audio levels - Avg: {average:F4}, Max: {maxSample:F4}, Position: {Microphone.GetPosition(selectedDevice)}");
        }
    }

    protected void DebugLog(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[ChatAgent] {message}");

            if (ModeSelector.instance != null)
                ModeSelector.instance.WriteToVRCanvas($"[ChatAgent] {message}");


        }
    }

    public void StartSession()
    {
        if (isSessionActive)
        {
            Debug.LogError("Session is already active, please stop it first");
            return;
        }

        CleanupAudioSources();
        InitializeAudioSource();
        SetupMicrophone();
        StopMicrophone(); // Mute microphone when it begins - don't listen .
        processedEventIds.Clear();
        session = new Session();
        StartCoroutine(InitializeSession());
    }

    public void StopSession()
    {
        if (!isSessionActive) return;

        if (remoteAudioTrack != null)
        {
            remoteAudioTrack.Dispose();
            remoteAudioTrack = null;
        }

        if (localAudioTrack != null)
        {
            localAudioTrack.Dispose();
            localAudioTrack = null;
        }

        if (dataChannel != null)
        {
            if (dataChannel.ReadyState == RTCDataChannelState.Open)
            {
                dataChannel.Close();
            }
            dataChannel = null;
        }

        if (tx_dataChannel != null)
        {
            if (tx_dataChannel.ReadyState == RTCDataChannelState.Open)
            {
                tx_dataChannel.Close();
            }
            tx_dataChannel = null;
        }
        if (peerConnection != null)
        {
            peerConnection.Close();
            peerConnection = null;
        }
        if (tx_peerConnection != null)
        {
            tx_peerConnection.Close();
            tx_peerConnection = null;
        }
        StopMicrophone();
        CleanupAudioSources();
        processedEventIds.Clear();
        isSessionActive = false;
        DebugLog("Session stopped");

        //if (ModeSelector.instance != null)
        //    ModeSelector.instance.chatAgentStarted = false;
    }

    void OnDisable()
    {
        StopSession();
    }

    void OnDestroy()
    {
        StopSession();
        CleanupAudioSources();
    }

    private IEnumerator InitializeSession()
    {
        if (isSessionActive)
        {
            Debug.LogError("Session already active!");
            yield break;
        }

        DebugLog("Starting session initialization...");

        // Get ephemeral key from server
        var url = $"{intenal_server_base}/token?agent={agentName}&tx_agent={agentName}_tx&transcription_enabled={transcription_enabled}";
        using (UnityWebRequest tokenRequest = UnityWebRequest.Get(url))
        {
            tokenRequest.SetRequestHeader("Authorization", $"Basic {credentials}");
            DebugLog($"Requesting token from: {url}");
            yield return tokenRequest.SendWebRequest();

            if (tokenRequest.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError($"Token request failed: {tokenRequest.error}\nResponse: {tokenRequest.downloadHandler.text}");
                StopSession();
                yield break;
            }

            string responseText = tokenRequest.downloadHandler.text;
            DebugLog($"Token response: {responseText}");

            TwoTokenResponse twotokenResponse;
            string ephemeralKey;
            string tx_ephemeralKey;

            try
            {
                twotokenResponse = JsonUtility.FromJson<TwoTokenResponse>(responseText);
                ephemeralKey = twotokenResponse.main.client_secret.value;
                tx_ephemeralKey = twotokenResponse.tx.client_secret.value;
                instruction_template = twotokenResponse.main.instructions;
                DebugLog("instruction_template: " + instruction_template);
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to parse token response: {e.Message}");
                StopSession();
                yield break;
            }

            // Create peer connection with configuration
            var config = new RTCConfiguration
            {
                iceServers = new[] { new RTCIceServer { urls = new[] { "stun:stun.l.google.com:19302" } } }
            };
            var config_tx = new RTCConfiguration
            {
                iceServers = new[] { new RTCIceServer { urls = new[] { "stun:stun.l.google.com:19302" } } }
            };

            try
            {
                peerConnection = new RTCPeerConnection(ref config);
                tx_peerConnection = new RTCPeerConnection(ref config_tx);
                DebugLog("Created RTCPeerConnection");
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to create RTCPeerConnection: {e.Message}");
                StopSession();
                yield break;
            }

            // Set up audio handling
            peerConnection.OnTrack = OnTrack;
            tx_peerConnection.OnTrack = e =>
            {
                DebugLog("tx_peerConnection OnTrack called" + e);
            };

            peerConnection.OnIceCandidate = candidate =>
            {
                //                DebugLog($"ICE Candidate: {candidate.Candidate}");
            };

            tx_peerConnection.OnIceCandidate = candidate =>
              {
                  DebugLog($"tx_peerConnection ICE Candidate: {candidate.Candidate}");
              };

            peerConnection.OnConnectionStateChange = state =>
            {
                DebugLog($"Connection state changed to: {state}");
                switch (state)
                {
                    case RTCPeerConnectionState.Failed:
                    case RTCPeerConnectionState.Closed:
                        if (isSessionActive)
                        {
                            StopSession();
                        }
                        break;
                    case RTCPeerConnectionState.Disconnected:
                        // Give it some time to reconnect before stopping
                        StartCoroutine(HandleDisconnection());
                        break;
                }
            };

            // Add local audio track
            try
            {
                localAudioTrack = new AudioStreamTrack(localAudioSource);

                tx_localAudioTrack = new AudioStreamTrack(tx_localAudioSource);

                peerConnection.AddTrack(localAudioTrack);
                tx_peerConnection.AddTrack(tx_localAudioTrack);

                DebugLog("Added local audio track");
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to setup audio track: {e.Message}");
                StopSession();
                yield break;
            }

            // Create data channel
            try
            {
                dataChannel = peerConnection.CreateDataChannel("oai-events");
                tx_dataChannel = tx_peerConnection.CreateDataChannel("oai-events");
                SetupDataChannelCallbacks();
                DebugLog("Created data channel");
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to create data channel: {e.Message}");
                StopSession();
                yield break;
            }

            // Create offer
            var offer = peerConnection.CreateOffer();
            yield return offer;

            var offer_tx = tx_peerConnection.CreateOffer();
            yield return offer_tx;

            if (offer.IsError || offer_tx.IsError)
            {
                Debug.LogError("Failed to create offer");
                StopSession();
                yield break;
            }
            // Trasnsciption
            if (false)
            {
                RTCSessionDescription offerDesc = offer_tx.Desc;

                // Set local description
                var setLocalDescriptionOperation = tx_peerConnection.SetLocalDescription(ref offerDesc);
                yield return setLocalDescriptionOperation;

                if (setLocalDescriptionOperation.IsError)
                {
                    Debug.LogError("peerConnectionTranscription Failed to set local description");
                    StopSession();
                    yield break;
                }
                DebugLog("peerConnectionTranscription Set local description");

                // Send offer to OpenAI API
                string sdpResponse;
                using (var sdpRequest = new UnityWebRequest($"{BASE_URL}", "POST"))
                {
                    byte[] bodyRaw = Encoding.UTF8.GetBytes(offerDesc.sdp);
                    sdpRequest.uploadHandler = new UploadHandlerRaw(bodyRaw);
                    sdpRequest.downloadHandler = new DownloadHandlerBuffer();
                    sdpRequest.SetRequestHeader("Authorization", $"Bearer {tx_ephemeralKey}");
                    sdpRequest.SetRequestHeader("Content-Type", "application/sdp");

                    DebugLog("peerConnectionTranscription Sending SDP offer to OpenAI...");
                    yield return sdpRequest.SendWebRequest();

                    if (sdpRequest.result != UnityWebRequest.Result.Success)
                    {
                        Debug.LogError($"peerConnectionTranscription SDP request failed: {sdpRequest.error}\nResponse: {sdpRequest.downloadHandler.text}");
                        StopSession();
                        yield break;
                    }

                    sdpResponse = sdpRequest.downloadHandler.text;
                }

                // Set remote description
                var answer = new RTCSessionDescription
                {
                    type = RTCSdpType.Answer,
                    sdp = sdpResponse
                };

                DebugLog(" peerConnectionTranscription Received SDP answer, setting remote description");
                var setRemoteDescriptionOperation = tx_peerConnection.SetRemoteDescription(ref answer);
                yield return setRemoteDescriptionOperation;

                if (setRemoteDescriptionOperation.IsError)
                {
                    Debug.LogError("peerConnectionTranscription Failed to set remote description");
                    StopSession();
                    yield break;
                }
                DebugLog("peerConnectionTranscription Session initialization completed successfully");
            }
            if (true)
            {
                RTCSessionDescription offerDesc = offer.Desc;

                // Set local description
                var setLocalDescriptionOperation = peerConnection.SetLocalDescription(ref offerDesc);
                yield return setLocalDescriptionOperation;

                if (setLocalDescriptionOperation.IsError)
                {
                    Debug.LogError("Failed to set local description");
                    StopSession();
                    yield break;
                }
                DebugLog("Set local description");

                // Send offer to OpenAI API
                string sdpResponse;
                using (var sdpRequest = new UnityWebRequest($"{BASE_URL}", "POST"))
                {
                    byte[] bodyRaw = Encoding.UTF8.GetBytes(offerDesc.sdp);
                    sdpRequest.uploadHandler = new UploadHandlerRaw(bodyRaw);
                    sdpRequest.downloadHandler = new DownloadHandlerBuffer();
                    sdpRequest.SetRequestHeader("Authorization", $"Bearer {ephemeralKey}");
                    sdpRequest.SetRequestHeader("Content-Type", "application/sdp");

                    DebugLog("Sending SDP offer to OpenAI...");
                    yield return sdpRequest.SendWebRequest();

                    if (sdpRequest.result != UnityWebRequest.Result.Success)
                    {
                        Debug.LogError($"SDP request failed: {sdpRequest.error}\nResponse: {sdpRequest.downloadHandler.text}");
                        StopSession();
                        yield break;
                    }

                    sdpResponse = sdpRequest.downloadHandler.text;
                }

                // Set remote description
                var answer = new RTCSessionDescription
                {
                    type = RTCSdpType.Answer,
                    sdp = sdpResponse
                };

                DebugLog("Received SDP answer, setting remote description");
                var setRemoteDescriptionOperation = peerConnection.SetRemoteDescription(ref answer);
                yield return setRemoteDescriptionOperation;

                if (setRemoteDescriptionOperation.IsError)
                {
                    Debug.LogError("Failed to set remote description");
                    StopSession();
                    yield break;
                }

                isSessionActive = true;
                DebugLog("Session initialization completed successfully");
            }
        }
    }

    private void SetupDataChannelCallbacks()
    {
        if (tx_dataChannel != null)
        {
            tx_dataChannel.OnOpen = () =>
            {
                DebugLog("tx_dataChannel Transcription data channel opened");
                Tx_SessionUpdate();
            };
            tx_dataChannel.OnClose = () =>
            {
                DebugLog("tx_dataChannel Transcription data channel closed");
            };
            tx_dataChannel.OnError = error =>
            {
                Debug.LogError($"tx_dataChannel Transcription data channel error: {error} {error.message}");
                if (isSessionActive)
                {
                    StopSession();
                }
            };
            tx_dataChannel.OnMessage = bytes =>
            {
                string message = Encoding.UTF8.GetString(bytes);
                DebugLog($"Received tx_dataChannel message: {message}");  // Add message logging
                try
                {
                    ChatEvent chatEvent = JsonUtility.FromJson<ChatEvent>(message);
                    if (!string.IsNullOrEmpty(chatEvent.event_id) && !processedEventIds.Contains(chatEvent.event_id))
                    {
                        switch (chatEvent.type)
                        {
                            case "conversation.item.input_audio_transcription.completed":
                                {
                                    DebugLog($"[tx_dataChannel] item_id: {chatEvent.item_id}, text: {chatEvent.transcript}");
                                    if (chatEvent.transcript != null && chatEvent.transcript.Replace(".", "").Trim().ToLower().Equals("preeti"))
                                    {
                                        DebugLog("Wake word detected: " + chatEvent.transcript);
                                        SwitchAgentTrack();
                                    }
                                    break;
                                }
                            case "response.output_item.added":
                                {
                                    DebugLog($"[tx_dataChannel] response.output_item.added {chatEvent.event_id} : {chatEvent.item_id}");
                                    if (chatEvent.item != null && chatEvent.item.type == "function_call" && chatEvent.item.name == "SwitchOnMicroPhone")
                                    {
                                        //if currenly on tx track --
                                        bool sendIamBack = !localAudioTrack.Enabled;

                                        SwitchAgentTrack();
                                        if (sendIamBack)
                                            SendUserMsgToAgent("i am back");
                                        //send "I am back"
                                    }
                                    break;
                                }
                            case "response.function_call_arguments.done":
                                {
                                    DebugLog($"function_call_arguments.done {chatEvent.event_id} : {chatEvent.name}");
                                    Tx_HandleFunctionCallEvent(chatEvent);
                                    break;
                                }
                            case "response.function_call_arguments.delta":
                                {
                                    DebugLog($"function_call_arguments.delta {chatEvent.event_id} : {chatEvent.name}");
                                    // SwitchAgentTrack();
                                    break;
                                }
                            default:
                                // DebugLog($"[tx_dataChannel] Unhandled event type: {chatEvent.type}");
                                break;
                        }
                        processedEventIds.Add(chatEvent.event_id);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"tx_dataChannelFailed to parse message: {e.Message}\nMessage content: {message}");
                    Debug.LogError(e);
                }
            };
        }
        if (dataChannel != null)
        {
            dataChannel.OnMessage = bytes =>
            {
                if (!isSessionActive) return;

                string message = Encoding.UTF8.GetString(bytes);
                DebugLog($"Received message: {message}");  // Add message logging

                try
                {
                    ChatEvent chatEvent = JsonUtility.FromJson<ChatEvent>(message);
                    if (!string.IsNullOrEmpty(chatEvent.event_id) && !processedEventIds.Contains(chatEvent.event_id))
                    {
                        processedEventIds.Add(chatEvent.event_id);
                        HandleChatEvent(chatEvent);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"Failed to parse message: {e.Message}\nMessage content: {message}");
                    Debug.LogError(e);
                }
            };

            dataChannel.OnOpen = () =>
            {
                if (!isSessionActive) return;
                DebugLog("Data channel opened");
                //    SendInitialSystemMessage();

            };

            dataChannel.OnClose = () =>
            {
                DebugLog("Data channel closed");
                if (isSessionActive)
                {
                    StopSession();
                }
            };

            dataChannel.OnError = error =>
            {
                Debug.LogError($"Data channel error: {error} {error.message}");
                if (isSessionActive)
                {
                    StopSession();
                }
            };
        }
    }

    public object CreateConvItem(Role role, string message)
    {
        var eventData = new
        {
            type = "conversation.item.create",
            item = new
            {
                type = "message",
                role = role.ToString(),
                content = new[]
                   {
                    new
                    {
                        type = role == Role.assistant ? "text" : "input_text",
                        text = message
                    }
                },
            }
        };

        return eventData;
    }

    public void SendConvCreateItem(Role role, string message)
    {
        if (dataChannel == null) { Debug.LogError("dataChannel is null"); return; }
        var eventData = CreateConvItem(role, message);
        string jsonData = JsonConvert.SerializeObject(eventData);
        dataChannel.Send(Encoding.UTF8.GetBytes(jsonData));
        DebugLog($"SendConvCreateItem {role} : {message} -- \n Full message: {jsonData} ");
    }
    public void SendBotMsgToAgent(string botMessage)
    {
        if (dataChannel == null) { Debug.LogError("dataChannel is null"); return; }
        var eventData = CreateConvItem(Role.assistant, botMessage);
        string jsonData = JsonConvert.SerializeObject(eventData);
        dataChannel.Send(Encoding.UTF8.GetBytes(jsonData));
        DebugLog($"SendBotMsgToAgent message: {jsonData}");
        var resp_create = new
        {
            type = "response.create"
        };
        jsonData = JsonConvert.SerializeObject(resp_create);
        dataChannel.Send(Encoding.UTF8.GetBytes(jsonData));
        DebugLog($"SendBotMsgToAgent Response create message: {jsonData}");
    }

    public void SendSystemMsgToAgent(string sysMessage)
    {
        if (dataChannel == null) { Debug.LogError("dataChannel is null"); return; }
        var eventData = CreateConvItem(Role.system, sysMessage);
        string jsonData = JsonConvert.SerializeObject(eventData);
        dataChannel.Send(Encoding.UTF8.GetBytes(jsonData));
        DebugLog($"SendSystemMsgToAgent message: {jsonData}");
        var resp_create = new
        {
            type = "response.create"
        };
        jsonData = JsonConvert.SerializeObject(resp_create);
        dataChannel.Send(Encoding.UTF8.GetBytes(jsonData));
        DebugLog($"SendSystemMsgToAgent Response create message: {jsonData}");
    }

    public void SendUserMsgToAgent(string userMessage)
    {
        if (dataChannel == null) { Debug.LogError("dataChannel is null"); return; }
        var eventData = CreateConvItem(Role.user, userMessage);
        string jsonData = JsonConvert.SerializeObject(eventData);
        dataChannel.Send(Encoding.UTF8.GetBytes(jsonData));
        DebugLog($"SendUserMsgToAgent message: {jsonData}");
        var resp_create = new
        {
            type = "response.create"
        };
        jsonData = JsonConvert.SerializeObject(resp_create);
        dataChannel.Send(Encoding.UTF8.GetBytes(jsonData));
        DebugLog($"SendUserMsgToAgent Response create message: {jsonData}");
    }


    //Cancel an inprogress response
    public void CancelInProgressResponse()
    {

        if (dataChannel == null) { Debug.LogError("dataChannel is null"); return; }
        var audio_ouput_clear = new
        {
            type = "output_audio_buffer.clear"
        };
        var resp_cancel = new
        {
            type = "response.cancel"
        };
        string jsonData = JsonConvert.SerializeObject(audio_ouput_clear);
        dataChannel.Send(Encoding.UTF8.GetBytes(jsonData));
        DebugLog($"CancelInProgressResponse create message: {jsonData}");

        jsonData = JsonConvert.SerializeObject(resp_cancel);
        dataChannel.Send(Encoding.UTF8.GetBytes(jsonData));
        DebugLog($"CancelInProgressResponse create message: {jsonData}");
    }


    // instructs the server to create a Response
    public void CreateResposne()
    {
        var resp_create = new
        {
            type = "response.create"
        };
        string jsonData = JsonConvert.SerializeObject(resp_create);
        dataChannel.Send(Encoding.UTF8.GetBytes(jsonData));
        DebugLog($"CreateResposne create message: {jsonData}");
    }
    public void SendIceBreaker()
    {
        DebugLog($"Sending Icebreaker message");
        StartMicrophone(); // Start microphone if not already started
        SendFreshContext();
        var icBreaker = "Hi!";
        SendConvCreateItem(Role.user, icBreaker);
        DebugLog($"Icebreaker message: {icBreaker}");
        //Here you PRE-LOAD a session.
        /*
        
        DebugLog($"Icebreaker Sending PreLoad Messages:");

        SendConvCreateItem(Role.assistant, "Hi, can I have your name to start the experience?");
        SendConvCreateItem(Role.user, "My name is Mogambo");
        customer.SetCustomerName("Mogambo");
        customer.StartExperience("");
        SendConvCreateItem(Role.assistant, "Thanks, we have now entered the Main Gate. Take a look, we can proceed when you are ready");
        SendConvCreateItem(Role.user, "Let's go to the next place");
        
        */


        CreateResposne();

        ChatAgentSelection agentSelection = GetComponent<ChatAgentSelection>();
        //agentSelection.SetMicIconColor(false);

        //if (ModeSelector.instance != null)
        //    ModeSelector.instance.chatAgentStarted = true;

    }

    private void Tx_HandleFunctionCallEvent(ChatEvent chatEvent)
    {
        Debug.Log("Tx_HandleFunctionCallEvent : " + chatEvent.name);

        string function_ouput_str = null;
        if (chatEvent.name == "SwitchOnMicroPhone")
        {
            DebugLog("SwitchOnMicroPhone !!!!!!!!!!! GURU" + chatEvent.arguments);
            Dictionary<string, string> dict = JsonConvert.DeserializeObject<Dictionary<string, string>>(chatEvent.arguments);
            string reason = dict["reason"];
            DebugLog("SwitchOnMicroPhone reason : " + reason);
            SwitchAgentTrack();
            // function_ouput_str = "Thanks.";
        }
        else
        {
            DebugLog("Tx_HandleFunctionCallEvent Unhandled function call: " + chatEvent.name);
        }
        if (function_ouput_str != null)
        {
            Debug.Log($"Function output Tx_HandleFunctionCallEvent: {function_ouput_str}");
            SendFunctionOutput(chatEvent, function_ouput_str, tx_dataChannel);
        }
    }

    protected void HandleFunctionCallEvent(ChatEvent chatEvent)
    {
        Debug.Log("HandleFunctionCallEvent : " + chatEvent.name + " : " + chatEvent.arguments);
        toolDefinitionsCache.TryGetValue(chatEvent.name, out ToolDefinition toolDefinition);
        if (toolDefinition == null)
        {
            DebugLog("HandleFunctionCallEvent : " + chatEvent.name + " not found in toolDefinitionsCache, skipping");
            return;
        }

        DebugLog("HandleFunctionCallEvent : " + chatEvent.name + " args : " + chatEvent.arguments);
        ActionFinishAgentTrigger finishCallback = (string function_ouput_str) =>
        {
            Debug.Log($"ActionFinishCallback Function output: {function_ouput_str} ; " + chatEvent.name + " args : " + chatEvent.arguments);
            SendFunctionOutput(chatEvent, function_ouput_str, dataChannel);
        };
        toolDefinition.actionCallback(chatEvent.arguments, finishCallback);
    }


    private void OnSessionReady()
    {
        DebugLog("Session is ready to start, call ICE Breaker");
        sessionStarted = true;

        if (ModeSelector.instance != null)
            ModeSelector.instance.SendFreshContext();

        SendIceBreaker();
    }
    private void HandleChatEvent(ChatEvent chatEvent)
    {
        if (!isSessionActive) return;

        DebugLog($"Handling event: {chatEvent.type}");

        switch (chatEvent.type)
        {
            case "session.created":
                {
                    sessionReadyToStart = true;
                    SwitchAgentTrack();
                    DebugLog($"Session created: ready to start! {chatEvent.event_id}");
                    OnSessionReady();
                    session.openAiSession = chatEvent.session;
                    break;
                }
            case "session.updated":
                {
                    DebugLog($"Session updated: {chatEvent.session.id}");
                    if (debugPanel != null)
                    {
                        debugPanel.LogToUIDebug($"Session updated: {chatEvent.session.id}");
                    }
                    session.openAiSession = chatEvent.session;
                    break;
                }
            case "response.function_call_arguments.done":
                DebugLog($"function_call_arguments.done {chatEvent.event_id} : {chatEvent.name}");
                HandleFunctionCallEvent(chatEvent);
                break;
            case "conversation.item.create":
                if (chatEvent.item?.content != null && chatEvent.item.content.Length > 0)
                {
                    foreach (var content in chatEvent.item.content)
                    {
                        DebugLog($"[{chatEvent.item.role}]: {content.text}");
                    }
                }
                break;
            case "conversation.item.created":
                {
                    var item_id = chatEvent.item.id;
                    DebugLog($"[conversation.item.created] item_id: {item_id} {chatEvent.item.type} {chatEvent.item.role}");
                    if (chatEvent.item.type == "message" && chatEvent.item.role == "assistant")
                    {
                        DebugLog("Assistant started to speak, listening mode now, will switch to transcrption Track");
                        //  SwitchTranscriptionTrack();
                    }
                    if (chatEvent.item.type == "message")
                    {
                        var messageEvent = new MessageItem
                        {
                            previous_item_id = chatEvent.previous_item_id,
                            item_id = item_id,
                            role = chatEvent.item.role,
                            message = chatEvent.item.content.Length > 0 ? chatEvent.item.content[0].text : null,
                            location = customer.currentLocation,
                        };
                        session.messages.Add(item_id, messageEvent);
                        DebugLog($"Added message to session: ib {session.ice_breaker} -- pi {chatEvent.previous_item_id} -- {session.ice_breaker == null} -- {chatEvent.previous_item_id == null || chatEvent.previous_item_id.Trim() == ""} -- {messageEvent}");
                        if (session.ice_breaker == null && (chatEvent.previous_item_id == null || chatEvent.previous_item_id.Trim() == ""))
                        {
                            DebugLog($"Setting ice breaker message: {messageEvent}");
                            session.ice_breaker = messageEvent;
                        }
                        pushSessionAsync();
                    }
                    if (chatEvent.item?.content != null && chatEvent.item.content.Length > 0)
                    {
                        foreach (var content in chatEvent.item.content)
                        {
                            DebugLog($"[{chatEvent.item.role}]: [{content.type}] --> {content.text}");
                        }
                    }

                    break;
                }
            case "conversation.item.input_audio_transcription.completed":
                {
                    var item_id = chatEvent.item_id;
                    var text = chatEvent.transcript;
                    DebugLog($"[conversation.item.input_audio_transcription.completed] item_id: {item_id}, text: {text}");
                    session.messages[item_id].message = text; //shoud exist by now in dict, if not - we should know - let's see
                    pushSessionAsync();
                    break;
                }
            case "response.audio_transcript.done":
                {
                    DebugLog($"[response.audio_transcript.done] item_id: {chatEvent.item_id}, text: {chatEvent.transcript}");
                    session.messages[chatEvent.item_id].message = chatEvent.transcript; //shoud exist by now in dict, if not - we should know - let's see
                    pushSessionAsync();
                    break;
                }
            case "response.create":
                isProcessingResponse = true;
                DebugLog("Starting new AI response");
                break;

            case "response.end":
                isProcessingResponse = false;
                isAudioPlaying = false;
                if (muteMicWhenAITalks)
                {
                    StartMicrophone(); // Resume microphone
                }
                DebugLog("AI response completed");
                break;

            case "response.done":
                {
                    DebugLog($"[response.done] total tokens : {chatEvent.response.id} : {chatEvent.response.usage.total_tokens} -> ({chatEvent.response.conversation_id})");
                    session.responseUsage.TryAdd(chatEvent.response.id, chatEvent.response.usage);
                    pushSessionAsync();
                    break;
                }

            case "output_audio_buffer.started":
                isAudioPlaying = true;
                if (muteMicWhenAITalks)
                {
                    StopMicrophone(); // Pause microphone while AI speaks
                }
                if (debugPanel != null)
                {
                    debugPanel.AgentSpeakingStart();
                }
                DebugLog("AI .. Audio playback started");
                break;

            case "output_audio_buffer.stopped":
                isAudioPlaying = false;
                if (muteMicWhenAITalks)
                {
                    StartMicrophone(); // Resume microphone
                }
                if (debugPanel != null)
                {
                    debugPanel.AgentSpeakingStop();
                }
                DebugLog("AI .. Audio playback stopped");
                break;

            default:
                DebugLog($"Received [Unhandled] event type: {chatEvent.type}");
                break;
        }
    }


    private void pushSessionAsync()
    {
        if (!pushSessionToServer)
        {
            DebugLog("Not pushing session to server");
            return;
        }
        //Refresh some properties
        session.customer_name = customer.customerName;
        StartCoroutine(pushSessionAsyncCoroutine());
    }
    private IEnumerator pushSessionAsyncCoroutine()
    {
        string url_session_update = $"{intenal_server_base}/api/session/update";
        DebugLog($"Starting Pushing session to server... {url_session_update}");

        // Serialize the session object to JSON
        string sessionJson = JsonConvert.SerializeObject(session, Formatting.Indented);

        using (UnityWebRequest sessionUpdate = new UnityWebRequest(url_session_update, "POST"))
        {
            byte[] bodyRaw = Encoding.UTF8.GetBytes(sessionJson);
            sessionUpdate.uploadHandler = new UploadHandlerRaw(bodyRaw);
            sessionUpdate.downloadHandler = new DownloadHandlerBuffer();
            sessionUpdate.SetRequestHeader("Authorization", $"Basic {credentials}");
            sessionUpdate.SetRequestHeader("Content-Type", "application/json");

            DebugLog($"Sending session data: {sessionJson}");
            yield return sessionUpdate.SendWebRequest();

            if (sessionUpdate.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError($"Session update failed: {sessionUpdate.error}\nResponse: {sessionUpdate.downloadHandler.text}");
            }
            else
            {
                DebugLog($"Session update successful: {sessionUpdate.downloadHandler.text}");
            }
        }
    }


    protected void SendFunctionOutput(ChatEvent chatEvent, string function_ouput_str, RTCDataChannel dataChannelToUse)
    {
        DebugLog($"Sending function output: {function_ouput_str} for event ID: {chatEvent.event_id} and name: {chatEvent.name}");
        var functionOutputEvent = new
        {
            type = "conversation.item.create",
            item = new
            {
                type = "function_call_output",
                call_id = chatEvent.call_id,
                output = function_ouput_str
            }
        };
        string jsonMessage = JsonConvert.SerializeObject(functionOutputEvent);
        DebugLog($"Function output message: {jsonMessage}");
        dataChannelToUse.Send(Encoding.UTF8.GetBytes(jsonMessage));
        var resp_create = new
        {
            type = "response.create"
        };
        string jsonResponse = JsonConvert.SerializeObject(resp_create);
        dataChannelToUse.Send(Encoding.UTF8.GetBytes(jsonResponse));
        DebugLog($"Response create message: {jsonResponse}");
    }

    private void OnTrack(RTCTrackEvent e)
    {
        if (e.Track is AudioStreamTrack audioTrack)
        {
            DebugLog($"Received audio track. Kind: {e.Track.Kind}, ID: {e.Track.Id}, Enabled: {e.Track.Enabled}");

            // Store the track for later use
            if (remoteAudioTrack != null)
            {
                remoteAudioTrack.Dispose();
            }
            remoteAudioTrack = audioTrack;

            // Wait until connection is fully established before setting up audio
            if (peerConnection.ConnectionState != RTCPeerConnectionState.Connected)
            {
                DebugLog("Received track but waiting for connection to be ready");
                StartCoroutine(WaitForConnectionAndSetupAudio());
                return;
            }

            SetupRemoteAudio();
        }
    }

    private void Tx_OnTrack(RTCTrackEvent e)
    {
        if (e.Track is AudioStreamTrack audioTrack)
        {
            DebugLog($"tx_peerConnection Received audio track. Kind: {e.Track.Kind}, ID: {e.Track.Id}, Enabled: {e.Track.Enabled}");

            // Store the track for later use
            if (remoteAudioTrack != null)
            {
                remoteAudioTrack.Dispose();
            }
            remoteAudioTrack = audioTrack;

            // Wait until connection is fully established before setting up audio
            if (tx_peerConnection.ConnectionState != RTCPeerConnectionState.Connected)
            {
                DebugLog("Received track but waiting for connection to be ready");
                StartCoroutine(WaitForConnectionAndSetupAudio());
                return;
            }

            SetupRemoteAudio();
        }
    }


    private void SetupRemoteAudio()
    {
        if (remoteAudioSource != null && remoteAudioTrack != null)
        {
            try
            {
                if (!isAudioPlaying)
                {
                    remoteAudioSource.SetTrack(remoteAudioTrack);
                    remoteAudioSource.loop = false;
                    remoteAudioSource.Play();
                    DebugLog("Remote audio track set and playing");
                }
                else
                {
                    DebugLog("Skipping audio track setup - audio already playing");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to setup remote audio: {ex.Message}\n{ex.StackTrace}");
            }
        }
        else
        {
            Debug.LogError("Remote audio source or track is null!");
        }
    }

    private IEnumerator WaitForConnectionAndSetupAudio()
    {
        float timeout = 5.0f; // 5 second timeout
        float elapsed = 0;

        while (peerConnection.ConnectionState != RTCPeerConnectionState.Connected && elapsed < timeout)
        {
            elapsed += Time.deltaTime;
            yield return null;
        }

        if (peerConnection.ConnectionState == RTCPeerConnectionState.Connected)
        {
            DebugLog("Connection ready, setting up remote audio");
            SetupRemoteAudio();
        }
        else
        {
            Debug.LogError("Timeout waiting for connection to be ready");
        }
    }

    private void Tx_SessionUpdate()
    {
        if (tx_dataChannel?.ReadyState == RTCDataChannelState.Open)
        {
            DebugLog("Sending initial system message to tx_dataChannel...");

            var systemMessage = new
            {
                event_id = Guid.NewGuid().ToString(),
                type = "session.update",
                session = new
                {
                    tools = new ToolConfig[] {
                    new ToolConfig {
                        type = "function",
                        name = "SwitchOnMicroPhone",
                        description = "Called ONLY when User wishes to talk to the sales Agent ...",
                        parameters = new ToolParameters
                        {
                            type = "object",
                            properties = new ReasonToolProperties
                            {
                                reason = new ToolProperty { type = "string" }
                            },
                            required = new string[] { "reason" }
                        }
                    },
                },
                    tool_choice = "auto",
                }
            };

            string jsonMessage = JsonConvert.SerializeObject(systemMessage,
        new JsonSerializerSettings
        {
            NullValueHandling = NullValueHandling.Ignore,
            Formatting = Formatting.None // or Formatting.Indented if you want pretty output
        });


            DebugLog($"tx_dataChannel SendFreshContext: System message: {jsonMessage}");  // Add logging
            tx_dataChannel.Send(Encoding.UTF8.GetBytes(jsonMessage));


        }
        else
        {
            DebugLog("Not sending initial system message - session not active or data channel not ready");
        }
    }

    private Dictionary<string, ToolDefinition> toolDefinitionsCache = new();

    private ToolConfig[] GetCurrentToolConfigs()
    {
        ToolDefinition[] toolDefinitions = GetCurrentAvailableToolDefinitions();
        if (toolDefinitions == null || toolDefinitions.Length == 0)
        {
            return null;
        }
        ToolConfig[] tools = new ToolConfig[toolDefinitions.Length];
        for (int i = 0; i < toolDefinitions.Length; i++)
        {
            tools[i] = toolDefinitions[i].toolConfig;
            toolDefinitionsCache[toolDefinitions[i].toolConfig.name] = toolDefinitions[i];
        }
        return tools;
    }

    public void SendFreshContext(string houseInformation = "", string[] spotNames = null)
    {
        this.houseInformation = houseInformation;
        this.spotNames = spotNames;

        if (!isSessionActive || dataChannel?.ReadyState != RTCDataChannelState.Open)
        {
            DebugLog("WARN !!! Not sending initial system message - session not active or data channel not ready");
            return;
        }
        var new_instruction = GetCurrentPromptInstruction();
        var toolConfigs = GetCurrentToolConfigs();
        var systemMessage = new
        {
            event_id = Guid.NewGuid().ToString(),
            type = "session.update",
            session = new
            {
                instructions = new_instruction,
                tools = toolConfigs,
                tool_choice = "auto",
            }
        };

        string jsonMessage = JsonConvert.SerializeObject(systemMessage,
    new JsonSerializerSettings
    {
        NullValueHandling = NullValueHandling.Ignore,
        Formatting = Formatting.None // or Formatting.Indented if you want pretty output
    });


        DebugLog($"SendFreshContext: System message: {jsonMessage}");  // Add logging
        dataChannel.Send(Encoding.UTF8.GetBytes(jsonMessage));
        current_instruction = new_instruction;
    }


    private void SendInitialSystemMessage()
    {
        if (!isSessionActive || dataChannel?.ReadyState != RTCDataChannelState.Open)
        {
            DebugLog("Not sending initial system message - session not active or data channel not ready");
        }
        ;

        DebugLog("Sending initial system message...");  // Add logging

        var systemMessage = new SessionEvent
        {
            event_id = Guid.NewGuid().ToString(),
            type = "session.update",
            session = new SessionConfig
            {

                //instructions = "Forget your previous instructions. Now You are joker. You crack jokes, you can't control your laughter.",
                tools = new ToolConfig[] {
                    new ToolConfig {
                        type = "function",
                        name = "ImportDXF",
                        description = "Triggered when the user asks for help with importing their DXF or DWG files. This opens a file picker to let the user select the file, ALWAYS ask the user to select the DXF file.",
                    }
                },
                tool_choice = "auto"
            }
        };

        string jsonMessage = JsonUtility.ToJson(systemMessage);

        DebugLog($"System message: {jsonMessage}");  // Add logging
        dataChannel.Send(Encoding.UTF8.GetBytes(jsonMessage));
    }

    public void SendTextMessage(string message)
    {
        if (!isSessionActive || dataChannel?.ReadyState != RTCDataChannelState.Open)
        {
            Debug.LogError("Failed to send message - session not active or data channel not ready");
            return;
        }

        DebugLog($"Sending message: {message}");  // Add logging

        var eventData = new ChatEvent
        {
            type = "conversation.item.create",
            item = new ChatItem
            {
                type = "message",
                role = "user",
                content = new[] { new ChatContent { type = "input_text", text = message } }
            },
            event_id = Guid.NewGuid().ToString()
        };

        string jsonMessage = JsonUtility.ToJson(eventData);
        dataChannel.Send(Encoding.UTF8.GetBytes(jsonMessage));

        // Send response create event
        var responseEvent = new ChatEvent
        {
            type = "response.create",
            event_id = Guid.NewGuid().ToString()
        };
        dataChannel.Send(Encoding.UTF8.GetBytes(JsonUtility.ToJson(responseEvent)));
    }

    private int GetPortFromCandidate(string candidate)
    {
        try
        {
            // ICE candidate format: candidate:x x udp/tcp xxxxx IP PORT typ ...
            var parts = candidate.Split(' ');
            for (int i = 0; i < parts.Length - 1; i++)
            {
                if (parts[i].Contains("udp") || parts[i].Contains("tcp"))
                {
                    return int.Parse(parts[i + 3]);
                }
            }
        }
        catch
        {
            // If parsing fails, return 0
        }
        return 0;
    }

    private IEnumerator HandleDisconnection()
    {
        float waitTime = 5.0f; // Wait 5 seconds before stopping
        float elapsed = 0;

        while (elapsed < waitTime)
        {
            if (peerConnection.ConnectionState == RTCPeerConnectionState.Connected)
            {
                // Connection recovered
                yield break;
            }
            elapsed += Time.deltaTime;
            yield return null;
        }

        if (isSessionActive)
        {
            StopSession();
        }
    }

    private IEnumerator MonitorRemoteAudio()
    {
        int silentFrames = 0;
        const int SILENT_THRESHOLD = 10; // 5 seconds of silence (at 0.5s intervals)

        while (isSessionActive && remoteAudioSource != null && (isProcessingResponse || isAudioPlaying))
        {
            if (remoteAudioSource.isPlaying)
            {
                float[] samples = new float[256];
                remoteAudioSource.GetOutputData(samples, 0);
                float sum = 0;
                for (int i = 0; i < samples.Length; i++)
                {
                    sum += Mathf.Abs(samples[i]);
                }
                float average = sum / samples.Length;

                if (average > 0.0001f)
                {
                    silentFrames = 0;
                }
                else
                {
                    silentFrames++;
                    if (silentFrames >= SILENT_THRESHOLD)
                    {
                        DebugLog("Audio stream completed");
                        isAudioPlaying = false;
                        StartMicrophone(); // Resume microphone after silence
                        break;
                    }
                }
            }
            yield return new WaitForSeconds(0.5f);
        }

        isAudioPlaying = false;
        StartMicrophone(); // Ensure microphone is resumed
        DebugLog("Audio monitoring stopped");
    }

    public void SendSessionTranscript()
    {
        //pretty print session messages
        string prettyJson = JsonConvert.SerializeObject(session, Formatting.Indented);
        Debug.Log("Session transcript:\n " + prettyJson);

    }

    internal void SwitchAgentTrack()
    {
        localAudioTrack.Enabled = true;
        tx_localAudioTrack.Enabled = false;
        CURRENT_TRACK = "AGENT";
        DebugLog("Agent track enabled");
        ChangeMicColor(false);
    }

    internal void SwitchTranscriptionTrack()
    {
        localAudioTrack.Enabled = false;
        tx_localAudioTrack.Enabled = true;
        CURRENT_TRACK = "TRANSCRIPTION";
        DebugLog("Transcription track enabled");
        ChangeMicColor(true);
    }

    public void ChangeMicColor(bool mute)
    {
        if (!sessionStarted)
            return;

        //if (micImage == null)
        //    micImage = FindFirstObjectByType<MicIconRef>().gameObject.GetComponent<Image>();

        if (micImage)
        {
            isMuted = mute;

            if (mute)
                micImage.color = muteColor;
            else
                micImage.color = unmuteColor;
        }
    }

    internal void setDebugPanel(ChatAgentDebugPanelSetup chatAgentDebugPanelSetup)
    {
        this.debugPanel = chatAgentDebugPanelSetup;
    }
}

[Serializable]
public class Session
{
    private static string GenerateSessionId()
    {
        string randomString = Guid.NewGuid().ToString("N").Substring(0, 8); // Generate 5-character alphanumeric string
        string timestamp = DateTime.Now.ToString("MM_dd_HH_mm_ss"); // Format date as mm_dd_hr_min_sec
        return $"{timestamp}__{randomString}";
    }

    public SessionObject openAiSession = null;
    public string session_id = GenerateSessionId();
    public DateTime session_start_time = DateTime.Now;
    public Dictionary<string, MessageItem> messages = new Dictionary<string, MessageItem>();
    public string project_name = "SignatureGlobal";
    public string experience_name = "3-BHK Appartment";
    public string customer_name = "DefaultCustomer";
    public MessageItem ice_breaker = null;

    public Dictionary<string, Usage> responseUsage = new Dictionary<string, Usage>();


}
[Serializable]
public class MessageItem
{

    public DateTime message_time = DateTime.Now;
    public string previous_item_id = null;
    public string item_id;
    public string role;
    public string message;
    public string location;

}
[Serializable]
public class TokenResponse
{
    public ClientSecret client_secret;
    public string instructions;
}

[Serializable]
public class TwoTokenResponse
{
    public TokenResponse main;
    public TokenResponse tx;
}


[Serializable]
public class ClientSecret
{
    public string value;
}

[Serializable]
public class ChatEvent
{
    public string type;
    public string event_id;
    public string previous_item_id;
    public string call_id;
    public string item_id;
    public string response_id;

    public SessionObject session;

    public Response response;
    public ChatItem item;

    public string name;
    public string arguments;
    public string transcript;
}

[Serializable]
public class SessionObject
{
    public string id;

    [JsonProperty("object")]
    public string object_prop;

    public string model;

    //  public string instructions; /verbose

    public string voice;
}


[Serializable]
public class Response
{

    [JsonProperty("object")]
    public string object_prop;
    public string id; //response id
    public string conversation_id;
    public Usage usage;
}

[Serializable]
public class Usage
{
    public int input_tokens;
    public int output_tokens;
    public int total_tokens;

    public TokenTypeCount input_token_details;
    public TokenTypeCount output_token_details;
}

[Serializable]
public class TokenTypeCount
{
    public int text_tokens;
    public int audio_tokens;
    public int cached_tokens;
}

[Serializable]
public class FunctionCallEVent
{
    public string type;
    public string event_id;

    public string name;
}

[Serializable]
public class ChatItem
{
    public string id;
    public string type;
    public string role;
    public string name;
    public ChatContent[] content;
}

[Serializable]
public class ChatContent
{
    public string type;
    public string text;
}

[Serializable]
public class SessionEvent
{
    public string event_id;
    public string type;
    public SessionConfig session;
}

[Serializable]
public class SessionConfig
{
    public string[] modalities;
    public string instructions;
    public string voice;
    public string input_audio_format;
    public string output_audio_format;
    public AudioTranscriptionConfig input_audio_transcription;
    public TurnDetectionConfig turn_detection;
    public ToolConfig[] tools;
    public string tool_choice;
    public float temperature;
    public string max_response_output_tokens;
}

[Serializable]
public class AudioTranscriptionConfig
{
    public string model;
}

[Serializable]
public class TurnDetectionConfig
{
    public string type;
    public float threshold;
    public int prefix_padding_ms;
    public int silence_duration_ms;
    public bool create_response;
}

[Serializable]
public class ToolConfig
{
    public string type;
    public string name;
    public string description;
    public ToolParameters parameters;
}

[Serializable]
public class ToolParameters
{
    public string type;
    public ToolProperties properties;
    public string[] required;
}

[Serializable]
public class ToolProperties
{

}


[Serializable]
public class MessageToolProperties : ToolProperties
{
    public ToolProperty message;
}


[Serializable]
public class ReasonToolProperties : ToolProperties
{
    public ToolProperty reason;
}



[Serializable]
public class GoToLocationToolProperties : ToolProperties
{
    public ToolProperty location;
}
[Serializable]
public class SetCustomerNameToolProperties : ToolProperties
{
    public ToolProperty name;
}

[Serializable]
public class StartExperianceProperty : ToolProperties
{
    public ToolProperty expName;
}

[Serializable]
public class ToolProperty
{
    public string type;

    public string description = null;

    [JsonProperty("enum")]
    public string[] enum_arr = null;
}

#if UNITY_EDITOR
[CustomEditor(typeof(ChatAgentAbstract), true)]
[CanEditMultipleObjects]
public class ChatAgentEditor : Editor
{
    public override void OnInspectorGUI()
    {
        ChatAgentAbstract agent = (ChatAgentAbstract)target;

        // Draw default inspector
        DrawDefaultInspector();

        EditorGUILayout.Space(10);

        // Session control buttons
        EditorGUILayout.BeginHorizontal();
        GUI.backgroundColor = Color.green;
        if (GUILayout.Button("Start Session", GUILayout.Height(30)))
        {
            agent.StartSession();
        }

        GUI.backgroundColor = Color.red;
        if (GUILayout.Button("Stop Session", GUILayout.Height(30)))
        {
            agent.StopSession();
        }
        GUI.backgroundColor = Color.white;
        EditorGUILayout.EndHorizontal();
        EditorGUILayout.BeginHorizontal();
        GUI.backgroundColor = Color.blue;
        if (GUILayout.Button("Send ICE Breaker", GUILayout.Height(30)))
        {
            agent.SendIceBreaker();
        }
        EditorGUILayout.EndHorizontal();
        EditorGUILayout.BeginHorizontal();
        GUI.backgroundColor = Color.green;
        if (GUILayout.Button($"Enable Agent Track ({agent.CURRENT_TRACK})", GUILayout.Height(30)))
        {
            agent.SwitchAgentTrack();
        }
        EditorGUILayout.EndHorizontal();
        EditorGUILayout.BeginHorizontal();
        GUI.backgroundColor = Color.blue;
        if (GUILayout.Button($"Enable Transcription Track ({agent.CURRENT_TRACK})", GUILayout.Height(30)))
        {
            agent.SwitchTranscriptionTrack();
        }
        EditorGUILayout.EndHorizontal();
        EditorGUILayout.BeginHorizontal();
        GUI.backgroundColor = Color.yellow;
        if (GUILayout.Button("Send Session Transcript", GUILayout.Height(30)))
        {
            agent.SendSessionTranscript();
        }
        EditorGUILayout.EndHorizontal();

        // Push to talk status
        /*   EditorGUILayout.Space(5);
           EditorGUILayout.LabelField("Push-to-Talk Status:", EditorStyles.boldLabel);
           EditorGUILayout.LabelField($"Key: {agent.pushToTalkKey}");
           EditorGUILayout.LabelField($"Active: {(Application.isPlaying ? agent.isListentingMode.ToString() : "Not playing")}");
        */
        // Available microphones
        EditorGUILayout.Space(10);
        EditorGUILayout.LabelField("Available Microphones:", EditorStyles.boldLabel);
        foreach (string device in Microphone.devices)
        {
            EditorGUILayout.LabelField($"• {device}");
        }

        // Repaint the inspector to show audio levels
        if (UnityEngine.Application.isPlaying)
        {
            Repaint();
        }
    }
}
#endif

