using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;
using System.Collections.Generic;
using System;


[System.Serializable]
public class ContentNode
{
    public string Label;
    public ContentNode Parent;
    public List<ContentNode> Children;

    public GameObject GameObjectToOpen;
    public System.Action OnClick;
    public ContentNode CachedSelectedChild;
    internal int Level;
    internal GameObject buttonGO;
    internal GameObject childContainer;
    

    public ContentNode(string label)
    {
        Label = label;
        Children = new List<ContentNode>();
    }        
}
public class KioskControlPanelManager : MonoBehaviour
{

    private Canvas targetCanvas;
    private RectTransform parentPanel;
    private List<GameObject> allChildContainers = new List<GameObject>();
    private Dictionary<ContentNode, GameObject> nodeToButtonMap = new Dictionary<ContentNode, GameObject>();

    [Header("Current Selection")]
    public ContentNode CurrentSelectedNode { get; private set; }
    private KioskUIManager kioskUIManager;

    //private GameObject overlayGO;
    private GameObject hamburgerButtonGO;
    private GameObject closeButtonGO;
    private RectTransform panelRect;
    private bool isPanelOpen = false;
    private float slideDuration = 0.3f;
    private RectTransform hierarchyScrollRect;


    private void Awake()
    {
        targetCanvas = GetComponent<Canvas>();
        kioskUIManager = GetComponent<KioskUIManager>();
    }

    public void InitializeHierarchyPanel()
    {
        // --- Create ScrollRect as the main panel ---
        GameObject scrollRectGO = new GameObject("HierarchyScrollRect", typeof(RectTransform), typeof(CanvasRenderer), typeof(Image), typeof(Mask), typeof(ScrollRect));
        scrollRectGO.transform.SetParent(targetCanvas.transform, false);
        var scrollRectRT = scrollRectGO.GetComponent<RectTransform>();
        scrollRectRT.anchorMin = new Vector2(1, 0);
        scrollRectRT.anchorMax = new Vector2(1, 1);
        scrollRectRT.pivot = new Vector2(1, 1);
        scrollRectRT.sizeDelta = new Vector2(320, 0);
        scrollRectRT.anchoredPosition = new Vector2(scrollRectRT.sizeDelta.x, 0); // Start off-screen right
        scrollRectRT.localScale = Vector3.one;

        hierarchyScrollRect = scrollRectRT;

        // Set up background and mask
        var scrollImage = scrollRectGO.GetComponent<Image>();
        scrollImage.color = new Color(0, 0, 0, 0.3f); // semi-transparent black
        var mask = scrollRectGO.GetComponent<Mask>();
        mask.showMaskGraphic = false; // Only show the background image

        // --- Create Content Panel (parentPanel) ---
        GameObject panelGO = new GameObject("HierarchyPanel", typeof(RectTransform));
        panelGO.transform.SetParent(scrollRectGO.transform, false);
        parentPanel = panelGO.GetComponent<RectTransform>();
        panelRect = parentPanel;
        parentPanel.anchorMin = new Vector2(0, 1);
        parentPanel.anchorMax = new Vector2(1, 1);
        parentPanel.pivot = new Vector2(0.5f, 1);
        parentPanel.anchoredPosition = Vector2.zero;
        parentPanel.sizeDelta = new Vector2(0, 0);
        parentPanel.localScale = Vector3.one;

        // Add VerticalLayoutGroup and ContentSizeFitter to content
        var layout = panelGO.AddComponent<VerticalLayoutGroup>();
        layout.childAlignment = TextAnchor.UpperCenter;
        layout.childForceExpandHeight = false;
        layout.childForceExpandWidth = true;
        layout.spacing = 5;

        var fitter = panelGO.AddComponent<ContentSizeFitter>();
        fitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;
        fitter.horizontalFit = ContentSizeFitter.FitMode.Unconstrained;

        // --- Set up ScrollRect ---
        var scrollRect = scrollRectGO.GetComponent<ScrollRect>();
        scrollRect.content = parentPanel;
        scrollRect.horizontal = false;
        scrollRect.vertical = true;
        scrollRect.movementType = ScrollRect.MovementType.Clamped;
        scrollRect.viewport = scrollRectRT;

        // --- Menu Button (Hamburger/Close) ---
        hamburgerButtonGO = CreateMenuButton("MenuButton", kioskUIManager.hamburgerIcon);
        hamburgerButtonGO.transform.SetParent(targetCanvas.transform, false);
        var mbRect = hamburgerButtonGO.GetComponent<RectTransform>();
        mbRect.anchorMin = new Vector2(1, 1);
        mbRect.anchorMax = new Vector2(1, 1);
        mbRect.pivot = new Vector2(1, 1);
        mbRect.sizeDelta = new Vector2(60, 60);
        mbRect.anchoredPosition = new Vector2(-10, -10);
        hamburgerButtonGO.GetComponent<Button>().onClick.AddListener(OpenPanel);

        // Remove closeButtonGO if you are using a single menu button as previously discussed
    }

    public void SpawnButtons(List<ContentNode> hierarchy)
    {
        if (parentPanel == null)
            InitializeHierarchyPanel();

        // Clear existing containers list, mapping, and current selection
        allChildContainers.Clear();
        nodeToButtonMap.Clear();
        CurrentSelectedNode = null;

        foreach (Transform child in parentPanel)
            DestroyImmediate(child.gameObject);
        foreach (var node in hierarchy)
        {
            InitializeCachedSelections(node);
            CreateButtonRecursive(node, parentPanel, 0);
        }
    }

    private void InitializeCachedSelections(ContentNode node)
    {
        // If this node has children, set the cached selection to the first child
        if (node.Children != null && node.Children.Count > 0)
        {
            node.CachedSelectedChild = node.Children[0];

            // Recursively initialize cached selections for all children
            foreach (var child in node.Children)
            {
                InitializeCachedSelections(child);
            }
        }
    }

    private void CreateButtonRecursive(ContentNode node, Transform parent, int level)
    {
        GameObject buttonGO = CreateTMPButton(node.Label, parent, level);
        //GameObject buttonGO = Instantiate(kioskUIManager.hierarchyButtonPrefab, parent);

        var button = buttonGO.GetComponent<Button>();
        node.Level = level;
        node.buttonGO = buttonGO;
        // Store the mapping for visual updates
        nodeToButtonMap[node] = buttonGO;

        if (node.OnClick != null)
            button.onClick.AddListener(() => node.OnClick.Invoke());

        if (node.Children != null && node.Children.Count > 0)
        {
            GameObject childContainer = new GameObject($"{node.Label}_Children", typeof(RectTransform));
            node.childContainer = childContainer;
            childContainer.transform.SetParent(parent);
            childContainer.transform.localScale = Vector3.one;

            var layout = childContainer.AddComponent<VerticalLayoutGroup>();
            layout.childForceExpandHeight = false;
            layout.childForceExpandWidth = true;
            layout.spacing = 4;
            // Add padding to child container to create hierarchy indentation
            layout.padding = new RectOffset(20, 0, 0, 0);

            var fitter = childContainer.AddComponent<ContentSizeFitter>();
            fitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;
            fitter.horizontalFit = ContentSizeFitter.FitMode.PreferredSize;

            var canvasGroup = childContainer.AddComponent<CanvasGroup>();
            canvasGroup.alpha = 0;
            childContainer.SetActive(false);
            
            allChildContainers.Add(childContainer);

            button.onClick.AddListener(() =>
            {
                Debug.Log($"Button clicked (with children): {node.Label}");
                if (!childContainer.activeSelf)
                {
                    // Close all other containers at the same level or higher
                    CollapseOtherHierarchies(childContainer, level);

                    // Open this container
                    childContainer.SetActive(true);
                    StartCoroutine(FadeIn(childContainer));

                    // If this node has a cached selected child, recursively open containers to that selection
                    if (node.CachedSelectedChild != null)
                    {
                        StartCoroutine(OpenCachedSelectionPath(node.CachedSelectedChild));
                    }
                }

                // Always set current selection (will find first leaf if this node has children)
                SetCurrentSelection(node, true);
            });

            foreach (var child in node.Children)
                CreateButtonRecursive(child, childContainer.transform, level + 1);
        }
        else
        {
            var layoutElement = buttonGO.GetComponent<LayoutElement>();
            layoutElement.minWidth = 200;
            layoutElement.minHeight = 100;

            // For buttons without children, still collapse other hierarchies at the same level
            button.onClick.AddListener(() =>
            {
                Debug.Log($"Button clicked (without children): {node.Label}");
                CollapseOtherHierarchies(null, level);
                SetCurrentSelection(node, true);
            });
        }
    }

    public void ShowContentNode(ContentNode contentNode, ActionFinishAgentTrigger finishTrigger)
    {
        foreach (var container in allChildContainers)
        {
            //  if (container == null || container == contentNode.childContainer || IsAncestorOf(container, contentNode.childContainer))
            //    continue;

            container.SetActive(false);
            // StartCoroutine(FadeOut(container));
        }
        SetAllParentContainersActive(contentNode);
        SetCurrentSelection(contentNode, false, finishTrigger);

    }

    private void SetAllParentContainersActive(ContentNode node)
    {
        if (node != null)
        {
            SetAllParentContainersActive(node.Parent);
            if (node.childContainer != null)
            {
                node.childContainer.SetActive(true);
                StartCoroutine(FadeIn(node.childContainer));
            }

        }
    }

    private ContentNode FindFirstLeafNode(ContentNode node)
    {
        if (node.Children == null || node.Children.Count == 0)
            return node;

        // Use cached selection if available, otherwise use first child
        ContentNode nextChild = node.CachedSelectedChild ?? node.Children[0];
        return FindFirstLeafNode(nextChild);
    }

    private void SetCurrentSelection
    (ContentNode node, bool throughButtonClick, ActionFinishAgentTrigger finishTrigger = null)
    {
        // Clear previous visual indicators
        UpdateVisualSelection(null);
        CurrentSelectedNode?.GameObjectToOpen?.SetActive(false);
        // If the node has children, find the first leaf node recursively
        ContentNode selectedLeaf = node.Children != null && node.Children.Count > 0
            ? FindFirstLeafNode(node)
            : node;

        CurrentSelectedNode = selectedLeaf;

        // Cache the selection path
        CacheSelectionPath(selectedLeaf);

        // Update visual indicators
        UpdateVisualSelection(selectedLeaf);
        if (CurrentSelectedNode.GameObjectToOpen != null)
        {
            CurrentSelectedNode.GameObjectToOpen.SetActive(true);
            KioskGenericConentNodeManager kioskGenericConentNodeManager = CurrentSelectedNode.GameObjectToOpen.GetComponent<KioskGenericConentNodeManager>();
            if (kioskGenericConentNodeManager != null)
            {
                kioskGenericConentNodeManager.ShowContent(CurrentSelectedNode.Label, throughButtonClick ? KioskEventSource.BUTTON_CLICK : KioskEventSource.AGENT_NAVIGATION, finishTrigger);
            }
            else
            {
                Debug.LogWarning("No KioskGenericConentNodeManager found on: " + CurrentSelectedNode.GameObjectToOpen.name);
            }
        }
        else
        {
            finishTrigger?.Invoke("Content [" + CurrentSelectedNode.Label + "] Not Found, please explain verbally.");
        }
        if (throughButtonClick)
        {
            if (kioskUIManager.chatAgent.isSessionActive)
            {
                kioskUIManager.chatAgent.CancelInProgressResponse();
                kioskUIManager.chatAgent.SendSystemMsgToAgent("UserNavigated to : " + CurrentSelectedNode.Label);
            }
            else
            {
                Debug.LogWarning("Chat Agent is not active, so not sending user navigations ");
            }
        }
        Debug.Log($"Current selection: {CurrentSelectedNode.Label} {throughButtonClick}");
    }

    private void CacheSelectionPath(ContentNode leafNode)
    {
        // Find all parent nodes that lead to this leaf and cache the selection
        foreach (var kvp in nodeToButtonMap)
        {
            var parentNode = kvp.Key;
            if (parentNode.Children != null && ContainsNodeInSubtree(parentNode, leafNode))
            {
                // Find which child leads to the leaf
                foreach (var child in parentNode.Children)
                {
                    if (child == leafNode || ContainsNodeInSubtree(child, leafNode))
                    {
                        parentNode.CachedSelectedChild = child;
                        break;
                    }
                }
            }
        }
    }

    private bool ContainsNodeInSubtree(ContentNode parent, ContentNode target)
    {
        if (parent == target) return true;
        if (parent.Children == null) return false;

        foreach (var child in parent.Children)
        {
            if (ContainsNodeInSubtree(child, target))
                return true;
        }
        return false;
    }

    private void UpdateVisualSelection(ContentNode selectedNode)
    {
        // Update all button texts to show/hide the "->" indicator
        foreach (var kvp in nodeToButtonMap)
        {
            var node = kvp.Key;
            var buttonGO = kvp.Value;
            var textComponent = buttonGO.GetComponentInChildren<TextMeshProUGUI>();

            if (textComponent != null)
            {
                string displayText = node == selectedNode ? $"-> {node.Label}" : node.Label;
                textComponent.text = displayText;
            }
        }
    }

    private IEnumerator OpenCachedSelectionPath(ContentNode cachedChild)
    {
        // Wait a frame to let the parent container finish opening
        yield return null;

        // Find the container for this cached child and open it if it has children
        if (cachedChild.Children != null && cachedChild.Children.Count > 0)
        {
            // Find the container GameObject for this node
            GameObject childContainer = FindContainerForNode(cachedChild);
            if (childContainer != null && !childContainer.activeSelf)
            {
                childContainer.SetActive(true);
                StartCoroutine(FadeIn(childContainer));

                // If this cached child also has a cached selection, continue recursively
                if (cachedChild.CachedSelectedChild != null)
                {
                    StartCoroutine(OpenCachedSelectionPath(cachedChild.CachedSelectedChild));
                }
            }
        }
    }

    private GameObject FindContainerForNode(ContentNode node)
    {
        // Look for a container with the naming pattern "{node.Label}_Children"
        string containerName = $"{node.Label}_Children";

        foreach (var container in allChildContainers)
        {
            if (container != null && container.name == containerName)
            {
                return container;
            }
        }

        return null;
    }

    private void CollapseOtherHierarchies(GameObject currentContainer, int currentLevel)
    {
        foreach (var container in allChildContainers)
        {
            if (container == currentContainer || container == null)
                continue;

            // Don't collapse ancestors of the current container (only if currentContainer is not null)
            if (currentContainer != null && IsAncestorOf(container, currentContainer))
                continue;

            int containerLevel = GetContainerLevel(container);

            // For buttons with children: close containers at same level or higher
            // For buttons without children: close containers at same level only
            bool shouldCollapse = currentContainer != null
                ? containerLevel <= currentLevel
                : containerLevel == currentLevel;

            if (shouldCollapse && container.activeSelf)
            {
                StartCoroutine(FadeOut(container));
            }
        }
    }

    private bool IsAncestorOf(GameObject potentialAncestor, GameObject child)
    {
        Transform current = child.transform.parent;

        while (current != null && current != parentPanel)
        {
            if (current.gameObject == potentialAncestor)
                return true;
            current = current.parent;
        }

        return false;
    }

    private int GetContainerLevel(GameObject container)
    {
        int level = 0;
        Transform current = container.transform.parent;

        // Count how many levels up we go until we reach the parentPanel
        while (current != null && current != parentPanel)
        {
            // Only count containers that are child containers (not buttons)
            if (current.name.EndsWith("_Children"))
                level++;
            current = current.parent;
        }

        return level;
    }

    //private GameObject CreateTMPButton(string label, Transform parent, int level)
    //{
    //    GameObject buttonGO = new GameObject($"Button_{label}", typeof(RectTransform), typeof(CanvasRenderer), typeof(Image), typeof(Button), typeof(HorizontalLayoutGroup));
    //    buttonGO.transform.SetParent(parent);

    //    var image = buttonGO.GetComponent<Image>();
    //    float shade = 0.85f - level * 0.05f;
    //    image.color = new Color(shade, shade, shade);

    //    var layoutGroup = buttonGO.GetComponent<HorizontalLayoutGroup>();
    //    layoutGroup.childAlignment = TextAnchor.MiddleLeft;
    //    layoutGroup.childForceExpandHeight = true;
    //    layoutGroup.childForceExpandWidth = true;
    //    // Basic padding for all buttons (hierarchy handled by container padding)
    //    layoutGroup.padding = new RectOffset(15, 10, 5, 5);
    //    layoutGroup.spacing = 10;

    //    var layoutElement = buttonGO.AddComponent<LayoutElement>();
    //    layoutElement.minHeight = 50;
    //    layoutElement.preferredHeight = 50;

    //    GameObject textGO = new GameObject("Label", typeof(RectTransform));
    //    textGO.transform.SetParent(buttonGO.transform);

    //    var text = textGO.AddComponent<TextMeshProUGUI>();
    //    text.text = label;
    //    text.enableAutoSizing = false;
    //    text.fontSize = 24;
    //    text.color = Color.black;
    //    text.alignment = TextAlignmentOptions.Center;

    //    var rect = textGO.GetComponent<RectTransform>();
    //    rect.anchorMin = new Vector2(0, 0);
    //    rect.anchorMax = new Vector2(1, 1);
    //    rect.offsetMin = Vector2.zero;
    //    rect.offsetMax = Vector2.zero;
    //    rect.sizeDelta = new Vector2(0, 0);

    //    return buttonGO;
    //}

    private GameObject CreateTMPButton(string label, Transform parent, int level)
    {
        // Instantiate the prefab as a child of the parent
        GameObject buttonGO = Instantiate(kioskUIManager.hierarchyButtonPrefab, parent);
        buttonGO.name = $"Button_{label}";

        // Set the label text using TextMeshProUGUI
        var textComponent = buttonGO.GetComponentInChildren<TextMeshProUGUI>();
        if (textComponent != null)
        {
            textComponent.text = label;
        }

        // Optionally, adjust the button's background color based on level
        var image = buttonGO.GetComponent<Image>();
        if (image != null)
        {
            float shade = 0.85f - level * 0.05f;
            image.color = new Color(shade, shade, shade);
        }

        return buttonGO;
    }

    private IEnumerator FadeIn(GameObject target)
    {
        var cg = target.GetComponent<CanvasGroup>();
        cg.alpha = 0;
        while (cg.alpha < 1)
        {
            cg.alpha += Time.deltaTime * 4;
            yield return null;
        }
        cg.alpha = 1;
    }

    private IEnumerator FadeOut(GameObject target)
    {
        var cg = target.GetComponent<CanvasGroup>();
        while (cg.alpha > 0)
        {
            cg.alpha -= Time.deltaTime * 4;
            yield return null;
        }
        cg.alpha = 0;
        target.SetActive(false);
    }

    private void OpenPanel()
    {
        if (isPanelOpen) return;
        isPanelOpen = true;
        // Change to close icon and event
        var img = hamburgerButtonGO.GetComponent<Image>();
        img.sprite = kioskUIManager.closeIcon;
        var btn = hamburgerButtonGO.GetComponent<Button>();
        btn.onClick.RemoveAllListeners();
        btn.onClick.AddListener(ClosePanel);
        StartCoroutine(SlidePanel(true));
    }

    private void ClosePanel()
    {
        if (!isPanelOpen) return;
        isPanelOpen = false;
        // Change to hamburger icon and event
        var img = hamburgerButtonGO.GetComponent<Image>();
        img.sprite = kioskUIManager.hamburgerIcon;
        var btn = hamburgerButtonGO.GetComponent<Button>();
        btn.onClick.RemoveAllListeners();
        btn.onClick.AddListener(OpenPanel);
        StartCoroutine(SlidePanel(false));
    }

    private IEnumerator SlidePanel(bool slideIn)
    {
        float elapsed = 0f;
        float width = hierarchyScrollRect.sizeDelta.x;
        Vector2 start = hierarchyScrollRect.anchoredPosition;
        Vector2 end = slideIn ? new Vector2(-50, 0) : new Vector2(width, 0);

        var mbRect = hamburgerButtonGO.GetComponent<RectTransform>();
        Vector2 mbStart = slideIn ? new Vector2(-10, -10) : new Vector2(-20 - width, -10);
        Vector2 mbEnd = slideIn ? new Vector2(-20 - width, -10) : new Vector2(-10, -10);

        mbRect.anchoredPosition = mbStart;

        while (elapsed < slideDuration)
        {
            float t = elapsed / slideDuration;
            hierarchyScrollRect.anchoredPosition = Vector2.Lerp(start, end, t);
            mbRect.anchoredPosition = Vector2.Lerp(mbStart, mbEnd, t);
            elapsed += Time.unscaledDeltaTime;
            yield return null;
        }
        hierarchyScrollRect.anchoredPosition = end;
        mbRect.anchoredPosition = mbEnd;
    }

    private GameObject CreateMenuButton(string name, Sprite icon)
    {
        var go = new GameObject(name, typeof(RectTransform), typeof(CanvasRenderer), typeof(Image), typeof(Button));
        var img = go.GetComponent<Image>();
        img.sprite = icon;
        img.type = Image.Type.Sliced;
        img.preserveAspect = true;
        return go;
    }
}