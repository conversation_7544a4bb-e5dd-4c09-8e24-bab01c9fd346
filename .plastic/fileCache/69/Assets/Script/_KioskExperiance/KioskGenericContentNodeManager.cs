using TMPro;
using UnityEngine;

public enum KioskEventSource
{
    AGENT_NAVIGATION,
    BUTTON_CLICK
}
public class KioskGenericConentNodeManager : MonoBehaviour
{

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {

    }

    public virtual void ShowContent(string content_label, KioskEventSource eventSource, ActionFinishAgentTrigger finishCallback = null)
    {
        gameObject.SetActive(true);
        Debug.Log("KioskGenericConentNodeManager : ShowContent : " + eventSource.ToString() + " : " + this.gameObject.name + " : " + content_label);
        finishCallback?.Invoke("Content [" + content_label + "] Shown, please continue.");
    }



    // Update is called once per frame
    void Update()
    {

    }
}
