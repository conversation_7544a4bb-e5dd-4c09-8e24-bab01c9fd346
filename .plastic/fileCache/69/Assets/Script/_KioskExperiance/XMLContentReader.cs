using System.Collections.Generic;
using System.IO;
using System.Xml;
using UnityEngine;

public class XMLContentReader
{
    public static List<ContentNode> CreateContentNodeTreeFromXML(string fileName)
    {
        //string xmlPath = Path.Combine(Application.dataPath, fileName);
        //string xmlContent = System.IO.File.ReadAllText(xmlPath);
        TextAsset xmlAsset = Resources.Load<TextAsset>(fileName);
        if (xmlAsset == null)
        {
            Debug.LogError($"XML file '{fileName}' not found in Resources.");
            return new List<ContentNode>();
        }
        string xmlContent = xmlAsset.text;

        XmlDocument xmlDoc = new XmlDocument();
        xmlDoc.LoadXml(xmlContent);

        XmlNode root = xmlDoc.DocumentElement;

        List<ContentNode> nodes = new List<ContentNode>();

        foreach (XmlNode child in root.ChildNodes)
        {
            ContentNode node = ParseXmlNode(child);
            if (node != null)
            {
                nodes.Add(node);
            }
        }

        return nodes;
    }

    private static ContentNode ParseXmlNode(XmlNode xmlNode)
    {
        // Skip text or empty nodes
        if (xmlNode.NodeType != XmlNodeType.Element)
            return null;

        // Use content_label attribute if available; else use the tag name
        string label = xmlNode.Attributes?["content_label"]?.Value ?? xmlNode.Name.Replace("_", " ");

        ContentNode contentNode = new ContentNode(label);

        foreach (XmlNode child in xmlNode.ChildNodes)
        {
            ContentNode childNode = ParseXmlNode(child);
            if (childNode != null)
            {
                contentNode.Children.Add(childNode);
            }
        }

        return contentNode;
    }
}
