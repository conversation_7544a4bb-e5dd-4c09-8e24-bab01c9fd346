using System;
using System.Collections;
using UnityEngine;

public class MiniatureSceneManager : KioskGenericConentNodeManager
{
    public string sceneName = "Miniature";

    TouchOrbitCamera touchOrbitCamera;
    //TouchOrbitCamera TouchOrbitCamera => touchOrbitCamera ??= FindFirstObjectByType<TouchOrbitCamera>();
    
    HotspotsManager hotspotsManager;
    //HotspotsManager HotspotsManager => hotspotsManager ??= FindFirstObjectByType<HotspotsManager>();

    AppController appController;
    //AppController AppController => appController ??= FindFirstObjectByType<AppController>();

    public override void ShowContent(string content_label, KioskEventSource eventSource, ActionFinishAgentTrigger finishTrigger = null)
    {
        base.ShowContent(content_label, eventSource, null); //send null - so that the finishCallback is not called

        Debug.Log($"MiniatureSceneManager: Event source : " + eventSource);
        Debug.Log($"MiniatureSceneManager: ShowContent called with label: {content_label}");

        string currentSceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;

        if (sceneName != currentSceneName)
        {
            Debug.Log($"Loading scene...: {sceneName}");
            SceneLoader.Instance.LoadSceneAsyncWithRenderedCallback(sceneName, () =>
            {
                Debug.Log($"Scene {sceneName} loaded successfully.");
                finishTrigger?.Invoke("Content [" + content_label + "] Shown, please continue.");

                if (content_label.Equals("Guided Tour"))
                {
                    if (appController == null)
                        appController = FindFirstObjectByType<AppController>();

                    appController.StartProjectOverview();
                }
                //else
                //    AppController.StartTowerPlans();
            }
            );
        }
        else
        {
            Debug.Log($"Scene {sceneName} is already loaded.");
            
            string towerName = RemovePlanKeyword(content_label);

            if (content_label.Equals("Guided Tour"))
            {
                if (appController == null)
                    appController = FindFirstObjectByType<AppController>();

                appController.StartProjectOverview();
            }
            else
            {

                if (hotspotsManager == null)
                    hotspotsManager = FindFirstObjectByType<HotspotsManager>();

                Transform hotspot = hotspotsManager.GetHotspotByName(towerName);
                if (touchOrbitCamera == null)
                    touchOrbitCamera = FindFirstObjectByType<TouchOrbitCamera>();

                touchOrbitCamera.StopAutoRotation();

                touchOrbitCamera.OrbitToTargetView(hotspot);
            }
            finishTrigger?.Invoke("Content [" + content_label + "] Shown, please continue.");
        }
    }

    void LoadSceneAsync(string sceneName)
    {
        Debug.Log($"Loading scene: {sceneName}");
        /*AsyncOperation asyncLoad = */
        UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(sceneName);

        //while (!asyncLoad.isDone)
        //{
        //    yield return null;
        //}

        Debug.Log($"Scene {sceneName} loaded successfully.");

    }

    

    private string RemovePlanKeyword(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return input;

        // Remove the word "plan" (case-insensitive), with optional whitespace before/after
        string result = System.Text.RegularExpressions.Regex.Replace(input, @"\bplan\b", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);

        // Trim any leading/trailing spaces
        return result.Trim();
    }

}
