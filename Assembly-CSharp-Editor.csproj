﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp/obj/$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp/bin/Debug/</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Assembly-CSharp-Editor</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_1_4;UNITY_6000_1;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_ACCESSIBILITY;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_ANDROID;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;ENABLE_ETC_COMPRESSION;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;PLATFORM_EXTENDS_VULKAN_PIPELINE_CACHE;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;PLATFORM_HAS_ADDITIONAL_API_CHECKS;PLATFORM_HAS_GRAPHICS_JOBS_SUPPORT_CHECK_OVERRIDE;PLATFORM_HAS_BUGGY_MSAA_RESOLVE;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_OSX;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;STARTER_ASSETS_PACKAGES_CHECKED;USE_INPUT_SYSTEM_POSE_CONTROL;FUSION_WEAVER;FUSION2;BAKERY_INCLUDED;BAKERY_NOREIMPORT;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER;UNITY_EDITOR_ONLY_COMPILATION</DefineConstants>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Editor:5</UnityProjectType>
    <UnityBuildTarget>Android:13</UnityBuildTarget>
    <UnityVersion>6000.1.4f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="/Users/<USER>/.vscode/extensions/visualstudiotoolsforunity.vstuc-1.1.2/Analyzers/Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hans/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/zh-Hans/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/PerfectCullingBakerHandle.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/Editor/DirectionalLayoutDragAndDrop.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingPostProcessScene.cs" />
    <Compile Include="Assets/Editor/LODSelectionOverride.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Baker/NativeVulkan/PerfectCullingBakerNativeVulkanWin64.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/PerfectCullingBakeAbortedYieldInstruction.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Baker/Native/PerfectCullingBakerNativeWin64.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Util/PerfectCullingMeshSplitting.cs" />
    <Compile Include="Assets/Editor/SetCustomGizmoIcon.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingExcludeVolumeEditor.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/Editor/DirectionalLayoutItemEditor.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingColorTableEditor.cs" />
    <Compile Include="Assets/Editor/RemoveNullMeshRenderers.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Baker/Native/PerfectCullingBakerNativeWin64Handle.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/Editor/DirectionalLayoutVolumeEditor.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/PerfectCullingSceneColor.cs" />
    <Compile Include="Assets/Editor/ZoneWithColliderTool.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Baker/Unity/PerfectCullingBakerUnity.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingCameraEditor.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Util/PerfectCullingClustering.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/EditorBake.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/Editor/LayoutifyToolEditor.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Baker/PerfectCullingBakerFactory.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingVolumeEditor.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingSettingsEditor.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingAlwaysIncludeVolumeEditor.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/PerfectCullingBakeNotStartedYieldInstruction.cs" />
    <Compile Include="Assets/Script/Flexlayout/Scripts/Editor/LightRigBuilder.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/PerfectCullingBakingManager.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/PerfectCullingBakeSettings.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/API/PerfectCullingAPI.cs" />
    <Compile Include="Assets/Editor/CoverBlockGenerator.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/PerfectCullingResourcesLocator.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingBakeDataEditor.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/bin/x64/pc_renderer.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/bin/x64/pc_renderer_vulkan.cs" />
    <Compile Include="Assets/Editor/TransformCopyPaste.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/PerfectCullingColorTable.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Util/PerfectCullingEditorUtil.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/Editor/RotationMappingGenerator.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingMenuOptions.cs" />
    <Compile Include="Assets/Editor/MeshInfo.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/BakeInformation.cs" />
    <Compile Include="Assets/Editor/MeshAssignerEditor.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Baker/Unity/PerfectCullingBakerUnityHandle.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingBakingManagerWindow.cs" />
    <Compile Include="Assets/Editor/ToolsMenu.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingVolumeSizeWindow.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingSceneCullingGroupEditor.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Baker/PerfectCullingBaker.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/Editor/SideMappingEditor.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/PerfectCullingSettings.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingBakeGroupWindow.cs" />
    <Compile Include="Assets/Editor/FindMissingScriptsWindow.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Editor/PerfectCullingRendererSelectionWindow.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/Editor/AnchorPickerWindow.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/PerfectCullingBakeHandle.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Baker/NativeVulkan/PerfectCullingBakerNativeVulkanWin64Handle.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Editor/Scripts/Baker/Unity/PerfectCullingBakerUnityCpuHandle.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets/Editor/x64/Bakery/frender.dll" />
    <None Include="Assets/Editor/x64/Bakery/optix.1.dll" />
    <None Include="Assets/Editor/x64/Bakery/d3dcompiler_47.dll" />
    <None Include="Assets/Editor/x64/Bakery/OpenImageDenoise_device_cuda.dll" />
    <None Include="Assets/Editor/x64/Bakery/shaderSrc/ftrace.cginc" />
    <None Include="Assets/Editor/x64/Bakery/cudnn64_7.dll" />
    <None Include="Assets/Editor/x64/Bakery/shaderSrc/ftTransformFarSphere.compute" />
    <None Include="Assets/Editor/x64/Bakery/uvrepack.dll" />
    <None Include="Assets/Editor/x64/Bakery/tbb.dll" />
    <None Include="Assets/Editor/x64/Bakery/alphabuffergen.dll" />
    <None Include="Assets/Editor/x64/Bakery/cudart64_91.dll" />
    <None Include="Assets/Editor/x64/Bakery/tbbbind_2_0.dll" />
    <None Include="Assets/Editor/x64/Bakery/OIDN2.dll" />
    <None Include="Assets/Editor/x64/Bakery/halffloat2vb.dll" />
    <None Include="Assets/Editor/x64/Bakery/tbbbind_2_5.dll" />
    <None Include="Assets/Editor/x64/Bakery/tbb12.dll" />
    <None Include="Assets/Editor/x64/Bakery/ftAtlas.shader" />
    <None Include="Assets/Editor/x64/Bakery/optix.51.dll" />
    <None Include="Assets/Editor/x64/Bakery/tbbbind.dll" />
    <None Include="Assets/Editor/x64/Bakery/shaderSrc/ftCullFarSphere.compute" />
    <None Include="Assets/Editor/x64/Bakery/simpleProgressBar.dll" />
    <None Include="Assets/Editor/x64/Bakery/ftChecker.shader" />
    <None Include="Assets/Editor/x64/Bakery/optix_denoiser.6.0.0.dll" />
    <None Include="Assets/Editor/x64/Bakery/lmrebake.dll" />
    <None Include="Assets/Editor/x64/Bakery/optix_denoiser.51.dll" />
    <None Include="Assets/Editor/x64/Bakery/OpenImageDenoise.dll" />
    <None Include="Assets/Editor/x64/Bakery/cudart64_90.dll" />
    <None Include="Assets/Editor/x64/Bakery/OpenImageDenoise_core.dll" />
    <None Include="Assets/Editor/x64/Bakery/optix.6.0.0.dll" />
    <None Include="Assets/Editor/x64/Bakery/cudart32_91.dll" />
    <None Include="Assets/Editor/x64/Bakery/uvgbuffergen.dll" />
    <None Include="Assets/Editor/x64/Bakery/tbbmalloc.dll" />
    <None Include="Assets/Editor/x64/Bakery/ftProjection.shader" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AMDModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AMDModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.NVIDIAModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WebGL.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.OSXStandalone.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options">
      <HintPath>Assets/Packages/Microsoft.Extensions.Options.9.0.1/lib/netstandard2.1/Microsoft.Extensions.Options.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder">
      <HintPath>Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encodings.Web">
      <HintPath>Assets/Packages/System.Text.Encodings.Web.9.0.1/lib/netstandard2.0/System.Text.Encodings.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions">
      <HintPath>Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection">
      <HintPath>Assets/Packages/Microsoft.Extensions.DependencyInjection.9.0.1/lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="websocket-sharp">
      <HintPath>Assets/Photon/PhotonLibs/WebSocket/websocket-sharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Fusion.Common">
      <HintPath>Assets/Photon/Fusion/Assemblies/Fusion.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NanoSockets">
      <HintPath>Assets/Photon/Fusion/Plugins/NanoSockets/NanoSockets/Dynamic/NanoSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource">
      <HintPath>Assets/Packages/System.Diagnostics.DiagnosticSource.9.0.1/lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives">
      <HintPath>Assets/Packages/Microsoft.Extensions.Primitives.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Memory">
      <HintPath>Assets/Packages/Microsoft.Extensions.Caching.Memory.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Fusion.Sockets">
      <HintPath>Assets/Photon/Fusion/Assemblies/Fusion.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Fusion.Log">
      <HintPath>Assets/Photon/Fusion/Assemblies/Fusion.Log.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Fusion.Runtime">
      <HintPath>Assets/Photon/Fusion/Assemblies/Fusion.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.TimeProvider">
      <HintPath>Assets/Packages/Microsoft.Bcl.TimeProvider.9.0.1/lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipelines">
      <HintPath>Assets/Packages/System.IO.Pipelines.9.0.1/lib/netstandard2.0/System.IO.Pipelines.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Photon3Unity3D">
      <HintPath>Assets/Photon/PhotonLibs/netstandard2.0/Photon3Unity3D.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Json">
      <HintPath>Assets/Packages/System.Net.Http.Json.9.0.1/lib/netstandard2.0/System.Net.Http.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PostHog">
      <HintPath>Assets/Packages/PostHog.1.0.5/lib/netstandard2.1/PostHog.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Cognitive3D.Newtonsoft.Json">
      <HintPath>Packages/com.cognitive3d.c3d-sdk/Editor/Plugins/Cognitive3D.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Http">
      <HintPath>Assets/Packages/Microsoft.Extensions.Http.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NuGetForUnity.PluginAPI">
      <HintPath>Library/PackageCache/com.github-glitchenzo.nugetforunity@762a2542a3e3/Editor/PluginAPI/NuGetForUnity.PluginAPI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Json">
      <HintPath>Assets/Packages/System.Text.Json.9.0.1/lib/netstandard2.0/System.Text.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>Assets/Packages/System.ComponentModel.Annotations.5.0.0/lib/netstandard2.1/System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Fusion.Realtime">
      <HintPath>Assets/Photon/Fusion/Assemblies/Fusion.Realtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Cognitive3D.GLTFSerialization">
      <HintPath>Packages/com.cognitive3d.c3d-sdk/Editor/Plugins/Cognitive3D.GLTFSerialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Abstractions">
      <HintPath>Assets/Packages/Microsoft.Extensions.Caching.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions">
      <HintPath>Assets/Packages/Microsoft.Extensions.DependencyInjection.Abstractions.9.0.1/lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Channels">
      <HintPath>Assets/Packages/System.Threading.Channels.9.0.1/lib/netstandard2.1/System.Threading.Channels.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Assets/Packages/Microsoft.Bcl.AsyncInterfaces.9.0.1/lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions">
      <HintPath>Assets/Packages/Microsoft.Extensions.Configuration.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging">
      <HintPath>Assets/Packages/Microsoft.Extensions.Logging.9.0.1/lib/netstandard2.1/Microsoft.Extensions.Logging.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TestRunner">
      <HintPath>Library/ScriptAssemblies/UnityEngine.TestRunner.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TestRunner">
      <HintPath>Library/ScriptAssemblies/UnityEditor.TestRunner.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Interaction.Toolkit.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.BuildingBlocks.DepthAPI.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.BuildingBlocks.DepthAPI.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Voice.Hub.Runtime">
      <HintPath>Library/ScriptAssemblies/Meta.Voice.Hub.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation">
      <HintPath>Library/ScriptAssemblies/Unity.AI.Navigation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.MultiplayerBlocks.Fusion.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.MultiplayerBlocks.Fusion.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.CoreUtils">
      <HintPath>Library/ScriptAssemblies/Unity.XR.CoreUtils.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.OVR">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.OVR.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="meta.xr.mrutilitykit.editor">
      <HintPath>Library/ScriptAssemblies/meta.xr.mrutilitykit.editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Platform.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.Platform.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.MultiplayerBlocks.Shared.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.MultiplayerBlocks.Shared.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi.TTS.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.TTS.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.BuildingBlocks">
      <HintPath>Library/ScriptAssemblies/Meta.XR.BuildingBlocks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="meta.xr.mrutilitykit">
      <HintPath>Library/ScriptAssemblies/meta.xr.mrutilitykit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library/ScriptAssemblies/Unity.Timeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ShaderGraph.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.ShaderLibrary">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Wit.Dictation">
      <HintPath>Library/ScriptAssemblies/Meta.Wit.Dictation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.VR.Scripts.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.VR.Scripts.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.GPUDriven.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Oculus">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Oculus.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Fbx">
      <HintPath>Library/ScriptAssemblies/Autodesk.Fbx.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PPv2URPConverters">
      <HintPath>Library/ScriptAssemblies/PPv2URPConverters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Telemetry">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Telemetry.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.OVR.Samples">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.OVR.Samples.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.VoiceSDK.Mic.Other">
      <HintPath>Library/ScriptAssemblies/Meta.VoiceSDK.Mic.Other.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi.Lib">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.Lib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Config.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.ImmersiveDebugger.Interface">
      <HintPath>Library/ScriptAssemblies/Meta.XR.ImmersiveDebugger.Interface.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.VoiceSDK.Mic.WebGL">
      <HintPath>Library/ScriptAssemblies/Meta.VoiceSDK.Mic.WebGL.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Collections.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="CesiumEditor">
      <HintPath>Library/ScriptAssemblies/CesiumEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpatialTracking">
      <HintPath>Library/ScriptAssemblies/UnityEngine.SpatialTracking.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Updater">
      <HintPath>Library/ScriptAssemblies/Unity.AI.Navigation.Updater.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Editor.PlayCompanion">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Editor.PlayCompanion.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Editor.Tags">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Editor.Tags.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Platform">
      <HintPath>Library/ScriptAssemblies/Oculus.Platform.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Fbx.Editor">
      <HintPath>Library/ScriptAssemblies/Autodesk.Fbx.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.VoiceSDK.Mic.Common">
      <HintPath>Library/ScriptAssemblies/Meta.VoiceSDK.Mic.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library/ScriptAssemblies/Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Management">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Management.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Runtime.Composer">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Runtime.Composer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AssistantCoreSDKEditor">
      <HintPath>Library/ScriptAssemblies/AssistantCoreSDKEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MetaXrSimulator.Editor">
      <HintPath>Library/ScriptAssemblies/MetaXrSimulator.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Oculus.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Oculus.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.ImmersiveDebugger.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.ImmersiveDebugger.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AssistantVoiceCommandCommon">
      <HintPath>Library/ScriptAssemblies/AssistantVoiceCommandCommon.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Guides.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Guides.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor.ConversionSystem">
      <HintPath>Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Voice.Hub.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.Voice.Hub.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Haptics.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.Haptics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Wit.Composer.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.Wit.Composer.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.OVR.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.OVR.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Audio.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Audio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="CesiumRuntime">
      <HintPath>Library/ScriptAssemblies/CesiumRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.ImmersiveDebugger">
      <HintPath>Library/ScriptAssemblies/Meta.XR.ImmersiveDebugger.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.WebRTC">
      <HintPath>Library/ScriptAssemblies/Unity.WebRTC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.InterfaceSupport">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.InterfaceSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.CoreUtils.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.CoreUtils.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpatialTracking">
      <HintPath>Library/ScriptAssemblies/UnityEditor.SpatialTracking.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.MultiplayerBlocks.Shared">
      <HintPath>Library/ScriptAssemblies/Meta.XR.MultiplayerBlocks.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Wit.Dictation.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.Wit.Dictation.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.2D.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.BuildingBlocks.DepthAPI">
      <HintPath>Library/ScriptAssemblies/Meta.XR.BuildingBlocks.DepthAPI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem.ForUI">
      <HintPath>Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library/ScriptAssemblies/UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Searcher.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Searcher.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.EnvironmentDepth">
      <HintPath>Library/ScriptAssemblies/Meta.XR.EnvironmentDepth.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.VR.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.VR.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Net.endel.nativewebsocket">
      <HintPath>Library/ScriptAssemblies/Meta.Net.endel.nativewebsocket.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipeline.Universal.ShaderLibrary">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Shaders">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.VR">
      <HintPath>Library/ScriptAssemblies/Oculus.VR.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi.Conduit">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.Conduit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime.Shared">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Timeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Editor.StatusMenu">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Editor.StatusMenu.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Wit.Composer">
      <HintPath>Library/ScriptAssemblies/Meta.Wit.Composer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor.Shared">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library/ScriptAssemblies/UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi.TTS">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.TTS.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.MultiplayerBlocks.Fusion">
      <HintPath>Library/ScriptAssemblies/Meta.XR.MultiplayerBlocks.Fusion.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAI.Lib.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.WitAI.Lib.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Mathematics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AssistantVoiceCommandCommon.Editor">
      <HintPath>Library/ScriptAssemblies/AssistantVoiceCommandCommon.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Editor.Callbacks">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Editor.Callbacks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Haptics">
      <HintPath>Library/ScriptAssemblies/Oculus.Haptics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XR.LegacyInputHelpers">
      <HintPath>Library/ScriptAssemblies/UnityEditor.XR.LegacyInputHelpers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.WebRTC.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.WebRTC.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections">
      <HintPath>Library/ScriptAssemblies/Unity.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.EnvironmentDepth.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.EnvironmentDepth.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Management.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Management.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XR.LegacyInputHelpers">
      <HintPath>Library/ScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Voice.VSDKHub.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.Voice.VSDKHub.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.Samples">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.Samples.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Editor">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.Samples.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.Samples.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi.Conduit.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.Conduit.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.MultiplayerBlocks.NGO.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.MultiplayerBlocks.NGO.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Common">
      <HintPath>Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.BuildingBlocks.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.BuildingBlocks.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Dictation.Runtime">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Dictation.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Interaction.Toolkit">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.EditorCoroutines.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NuGetForUnity">
      <HintPath>Library/ScriptAssemblies/NuGetForUnity.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.AI.Navigation.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics">
      <HintPath>Library/ScriptAssemblies/Unity.Mathematics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Editor.UserInterface">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Editor.UserInterface.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Interaction.Toolkit.Analytics.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.Analytics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Editor.Composer">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Editor.Composer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Formats.Fbx.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.Formats.Fbx.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Runtime">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Burst.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Formats.Fbx.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Formats.Fbx.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>Library/ScriptAssemblies/Unity.Burst.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Audio">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Audio.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AssistantCoreSDKRuntime">
      <HintPath>Library/ScriptAssemblies/AssistantCoreSDKRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Dictation.Editor">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Dictation.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>Library/ScriptAssemblies/Unity.InputSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp-firstpass.csproj" />
    <ProjectReference Include="Assembly-CSharp.csproj" />
    <ProjectReference Include="Assembly-CSharp-Editor-firstpass.csproj" />
    <ProjectReference Include="Fusion.Unity.csproj" />
    <ProjectReference Include="BakeryRuntimeAssembly.csproj" />
    <ProjectReference Include="Cognitive3D.csproj" />
    <ProjectReference Include="BakeryEditorAssembly.csproj" />
    <ProjectReference Include="Fusion.Unity.Editor.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
