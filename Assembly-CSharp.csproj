﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp/obj/$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp/bin/Debug/</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_1_4;UNITY_6000_1;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_ACCESSIBILITY;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_ANDROID;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;ENABLE_ETC_COMPRESSION;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;PLATFORM_EXTENDS_VULKAN_PIPELINE_CACHE;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;PLATFORM_HAS_ADDITIONAL_API_CHECKS;PLATFORM_HAS_GRAPHICS_JOBS_SUPPORT_CHECK_OVERRIDE;PLATFORM_HAS_BUGGY_MSAA_RESOLVE;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_OSX;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;STARTER_ASSETS_PACKAGES_CHECKED;USE_INPUT_SYSTEM_POSE_CONTROL;FUSION_WEAVER;FUSION2;BAKERY_INCLUDED;BAKERY_NOREIMPORT;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>Android:13</UnityBuildTarget>
    <UnityVersion>6000.1.4f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="/Users/<USER>/.vscode/extensions/visualstudiotoolsforunity.vstuc-1.1.2/Analyzers/Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hans/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/zh-Hans/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <Analyzer Include="/Users/<USER>/code/mhxp/MHXP_Demo_V2/Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingTerrain.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/PerfectCullingVolume.cs" />
    <Compile Include="Assets/Script/Analytics/CognetiveManagerWrapper.cs" />
    <Compile Include="Assets/Script/Spatial Anchors/RefrenceCatch/MeshParent.cs" />
    <Compile Include="Assets/Script/ToggleElement.cs" />
    <Compile Include="Assets/Script/_KioskExperiance/XMLContentReader.cs" />
    <Compile Include="Assets/Script/Spatial Anchors/RefrenceCatch/AnchorShadowRef.cs" />
    <Compile Include="Assets/Script/LoaderInit.cs" />
    <Compile Include="Assets/Script/_KioskExperiance/contents/ananda/MiniatureSceneManager.cs" />
    <Compile Include="Assets/Script/Utilities/Interaction.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingBakeData.cs" />
    <Compile Include="Assets/Script/Hotspots/RuntimeHotspot.cs" />
    <Compile Include="Assets/Script/UI/EyeBlinkSimulator.cs" />
    <Compile Include="Assets/Script/UI/VRLogMessage.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/UIStateController.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingRendererTag.cs" />
    <Compile Include="Assets/Script/ChatAgent/Gaze/AIInspectable.cs" />
    <Compile Include="Assets/Script/LevelInIt.cs" />
    <Compile Include="Assets/Script/BuildPipeline/MHXPBuildSettings.cs" />
    <Compile Include="Assets/Script/_KioskExperiance/KioskUIManager.cs" />
    <Compile Include="Assets/Script/_TabletMode/Map/BillboardLookAt.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/DirectionalLayoutVolume.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/SamplingProviders/ExcludeBelowColliderSamplingProvider.cs" />
    <Compile Include="Assets/Script/Utilities/Movement.cs" />
    <Compile Include="Assets/Script/_TabletMode/Utilities/HotspotDataHolder.cs" />
    <Compile Include="Assets/Script/Utilities/ReuseLightmapOnLODs.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/SceneList.cs" />
    <Compile Include="Assets/Script/_TabletMode/Utilities/OnClickCallback.cs" />
    <Compile Include="Assets/Script/_KioskExperiance/KioskContentAdapter.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Demos/Scripts/RedCube.cs" />
    <Compile Include="Assets/Script/Hotspots/VRControllerCheatCode.cs" />
    <Compile Include="Assets/Script/Hotspots/BoundaryGenerator3D.cs" />
    <Compile Include="Assets/Script/Hotspots/HotspotsManager.cs" />
    <Compile Include="Assets/Script/ChatAgent/ChatAgentKiosk.cs" />
    <Compile Include="Assets/Script/BuildPipeline/MHXPData.cs" />
    <Compile Include="Assets/Script/Spatial Anchors/SpatialAnchorCoreBuildingBlockInstance.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/IActiveSamplingProvider.cs" />
    <Compile Include="Assets/Script/Spatial Anchors/MeshAllignerRefrenceCatch.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingBakingBehaviour.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/SideOffset.cs" />
    <Compile Include="Assets/Script/_TabletMode/CameraMovement/CameraTouchAndMouseRotate.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/PerfectCullingExcludeVolume.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/TerrainToMeshUtility.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/DirectionalLayoutItem.cs" />
    <Compile Include="Assets/Script/_KioskExperiance/FaceRecognizer.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Demos/Scripts/FlyCamera.cs" />
    <Compile Include="Assets/Script/Hostspot/HotspotUIManager.cs" />
    <Compile Include="Assets/Script/Hotspots/LevelStartRefrenceCatch.cs" />
    <Compile Include="Assets/Script/_TabletMode/UI/UIManagerTabletMode.cs" />
    <Compile Include="Assets/Transition_tessellation/Scripts/TrasitionMesh.cs" />
    <Compile Include="Assets/Script/Config/ConfigDataSerializer.cs" />
    <Compile Include="Assets/Script/Spatial Anchors/RefrenceCatch/CameraRef.cs" />
    <Compile Include="Assets/Script/Spatial Anchors/RefrenceCatch/LevelStartRef.cs" />
    <Compile Include="Assets/Script/CarReferenceCatch.cs" />
    <Compile Include="Assets/Script/Config/Model/ConfigData.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/SamplingProviderBase.cs" />
    <Compile Include="Assets/Script/ChatAgent/Customer.cs" />
    <Compile Include="Assets/Script/Utilities/FileOperations.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/SideMapping.cs" />
    <Compile Include="Assets/Script/LevelsAndZonesSystem/ZoneDataSerializer.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/DirectionalLayoutSaver.cs" />
    <Compile Include="Assets/Script/_KioskExperiance/KioskControlPanelManager.cs" />
    <Compile Include="Assets/Script/LevelsAndZonesSystem/Zones.cs" />
    <Compile Include="Assets/Script/DataController.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/Logger/Logger.cs" />
    <Compile Include="Assets/Script/UI/Carousel/CarouselManager.cs" />
    <Compile Include="Assets/Script/ChatAgent/ChatAgentDebugPanelSetup.cs" />
    <Compile Include="Assets/Script/Utilities/InspectorButtonAttribute.cs" />
    <Compile Include="Assets/Script/_KioskExperiance/KioskGenericContentNodeManager.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/VRNetworkObject.cs" />
    <Compile Include="Assets/Script/Hotspots/SwitchBalcony.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/Side.cs" />
    <Compile Include="Assets/Script/Spatial Anchors/TestInstantiate.cs" />
    <Compile Include="Assets/Script/Spatial Anchors/DontDestroyAnchors.cs" />
    <Compile Include="Assets/Script/_TabletMode/CameraMovement/CameraMovement.cs" />
    <Compile Include="Assets/Script/Spatial Anchors/AnchorControllerInstance.cs" />
    <Compile Include="Assets/Script/_TabletMode/CameraMovement/OrbitCamera/TouchOrbitCamera.cs" />
    <Compile Include="Assets/Script/Analytics/PosthogAdapter.cs" />
    <Compile Include="Assets/Script/LevelsAndZonesSystem/LevelBehaviour.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingUtil.cs" />
    <Compile Include="Assets/Script/Hotspots/LevelStartShadowRef.cs" />
    <Compile Include="Assets/Script/Flexlayout/Scripts/RectList.cs" />
    <Compile Include="Assets/Script/Spatial Anchors/AlignMesh.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/RPCInteractionSource.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/EEmptyCellCullBehaviour.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingPortalCell.cs" />
    <Compile Include="Assets/Script/Flexlayout/Scripts/LightingManager.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/LayoutifyTool.cs" />
    <Compile Include="Assets/Script/Hotspots/Showcase_Minimap/ShowcaseUIHighlight.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/SamplingProviders/ExcludeTooFarFromNavMeshSamplingProvider.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/PerfectCullingAlwaysIncludeVolume.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/FusionSceneManagement.cs" />
    <Compile Include="Assets/Script/_TabletMode/CameraMovement/ChangeCameraProperties.cs" />
    <Compile Include="Assets/Script/ToggleInteriors.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/ConnectToPhoton.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/SamplingProviders/ExcludeFloatingSamplingProvider.cs" />
    <Compile Include="Assets/Script/Hotspots/HotspotSetManager.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingTemp.cs" />
    <Compile Include="Assets/Script/_TabletMode/CameraMovement/RuntimeEditorCamera.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/EBakeRenderMode.cs" />
    <Compile Include="Assets/Script/ChatAgent/MicRouterExperiment.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/CustomHandle.cs" />
    <Compile Include="Assets/Script/_TabletMode/Map/CesiumPOIManager.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingRenderToggleMode.cs" />
    <Compile Include="Assets/Script/UI/HotspotNameButtonParentRef.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingMonoGroup.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/SceneManagerMHXP.cs" />
    <Compile Include="Assets/Script/_TabletMode/AppController.cs" />
    <Compile Include="Assets/Script/_TabletMode/UI/UImangerProjectLevel.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingLogger.cs" />
    <Compile Include="Assets/Script/_TabletMode/CameraMovement/OrbitCamera/MapImageController.cs" />
    <Compile Include="Assets/Script/UI/ToggleSwitch.cs" />
    <Compile Include="Assets/Script/Analytics/AnalyticsManager.cs" />
    <Compile Include="Assets/Script/LevelsAndZonesSystem/FloorLevelChanges.cs" />
    <Compile Include="Assets/Script/ChatAgent/ChatAgentHomeShowcase.cs" />
    <Compile Include="Assets/Script/_TabletMode/CameraMovement/OrbitCamera/ZoomFadeController.cs" />
    <Compile Include="Assets/Script/UI/InputFeildClicked.cs" />
    <Compile Include="Assets/Script/_TabletMode/DataController.cs" />
    <Compile Include="Assets/Script/_TabletMode/SceneLoader.cs" />
    <Compile Include="Assets/Script/Spatial Anchors/ButtonMapperInstance.cs" />
    <Compile Include="Assets/Script/_TabletMode/TapAndSwipeController.cs" />
    <Compile Include="Assets/Script/UI/VRCanvas.cs" />
    <Compile Include="Assets/Script/UI/Carousel/CarouselSwipeDetector.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingBakeGroup.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/DirectionalLayoutData.cs" />
    <Compile Include="Assets/Transition_tessellation/Scripts/ToggleManager.cs" />
    <Compile Include="Assets/Script/Utilities/StealLightmap.cs" />
    <Compile Include="Assets/Script/UI/VRBUttonParentRef.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/PerfectCullingVolumeBakeData.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingMath.cs" />
    <Compile Include="Assets/Script/Spatial Anchors/RefrenceCatch/VRRigCameraRef.cs" />
    <Compile Include="Assets/Script/_KioskExperiance/contents/ananda/VirtualHomeManager.cs" />
    <Compile Include="Assets/Script/Utilities/MinimapMapper.cs" />
    <Compile Include="Assets/Script/Hotspots/CycleThroughHotspots.cs" />
    <Compile Include="Assets/Transition_tessellation/Scripts/RevealTessellation.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/SamplingProviders/ExcludeBelowColliderArraySamplingProvider.cs" />
    <Compile Include="Assets/Script/TestData/TestCanvasDataViewer.cs" />
    <Compile Include="Assets/Transition_tessellation/Scripts/FadeScript.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/IO/PerfectCullingBitStreams.cs" />
    <Compile Include="Assets/Script/_TabletMode/PlayerHeightController.cs" />
    <Compile Include="Assets/Script/UI/ToggleSwitchGroupManager.cs" />
    <Compile Include="Assets/Script/_TabletMode/UI/IntroSceneButtonClicks.cs" />
    <Compile Include="Assets/Script/Hotspots/HierarchyGizmosDirections.cs" />
    <Compile Include="Assets/Script/LevelsAndZonesSystem/ZoneController.cs" />
    <Compile Include="Assets/Script/ChatAgent/ChatAgentSelection.cs" />
    <Compile Include="Assets/Script/UI/Carousel/CarouselDemoSetup.cs" />
    <Compile Include="Assets/Script/Utilities/SetTextForMinimapButtons.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingSceneGroup.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Camera/PerfectCullingCamera.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/UIManger.cs" />
    <Compile Include="Assets/Script/ChatAgent/ListenerAgent.cs" />
    <Compile Include="Assets/Script/Utilities/Serializer.cs" />
    <Compile Include="Assets/Script/ChatAgent/ChatAgentAbstract.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/DirectionalLayoutAnchor.cs" />
    <Compile Include="Assets/Script/Flexlayout/Scripts/MeshZoneDivider.cs" />
    <Compile Include="Assets/Script/LiftRefrenceCatch.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/Logger/ToastManager.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/ConnectionManager.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/SamplingProviders/ExcludeInsideCollidersSamplingProvider.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/NetworkTransformSync.cs" />
    <Compile Include="Assets/Script/Spatial Anchors/RefrenceCatch/AnchorRef.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/CenterEyeAnchorRefrence.cs" />
    <Compile Include="Assets/Script/UI/Carousel/MediaItem.cs" />
    <Compile Include="Assets/Script/BuildPipeline/MHXPBuildSettingsEditor.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingConstants.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/DefaultActiveSamplingProvider.cs" />
    <Compile Include="Assets/Script/Config/Model/DeviceToRoomMapping.cs" />
    <Compile Include="Assets/Scripts/DirectionalLayoutSystem/DirectionalLayoutLoader.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/SetDeviceMapping.cs" />
    <Compile Include="Assets/Script/Tests/TestUIScripts.cs" />
    <Compile Include="Assets/Script/ChatAgent/Gaze/GazeDetector.cs" />
    <Compile Include="Assets/Script/_KioskExperiance/contents/ananda/AnandaKioskContent.cs" />
    <Compile Include="Assets/Script/Config/Model/UserInfo.cs" />
    <Compile Include="Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingVisibilityLayer.cs" />
    <Compile Include="Assets/Script/Hotspots/EnaableWarning.cs" />
    <Compile Include="Assets/Script/Multiplayer/Scripts/ModeSelector.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/TMPro.cginc" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Caching.Memory.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Photon/PhotonLibs/netstandard2.0/Photon3Unity3D.xml" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Net.Http.Json.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/lib/netstandard2.1/Microsoft.Extensions.Options.xml" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/lib/netstandard2.1/Microsoft.Extensions.Options.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile-2-Pass.shader" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/System.Text.Encodings.Web.9.0.1/lib/netstandard2.0/System.Text.Encodings.Web.dll" />
    <None Include="Assets/Packages/System.IO.Pipelines.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/System.Threading.Channels.9.0.1/lib/netstandard2.1/System.Threading.Channels.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/System.IO.Pipelines.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/System.Threading.Channels.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Caching.Memory.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml" />
    <None Include="Assets/Packages/System.Text.Encodings.Web.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_Bitmap.shader" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Abstractions.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Abstractions.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Diagnostics.DiagnosticSource.9.0.1/Sources/ILLink.Descriptors.LibraryBuild.xml" />
    <None Include="Assets/Packages/System.Text.Encodings.Web.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Photon/Fusion/Assemblies/Fusion.Realtime.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Http.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Script/_KioskExperiance/contents/ananda/PromptBackups.txt" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.DependencyInjection.Abstractions.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/System.IO.Pipelines.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/System.Diagnostics.DiagnosticSource.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.DependencyInjection.9.0.1/lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/TMPro_Mobile.cginc" />
    <None Include="Assets/Packages/Microsoft.Extensions.Caching.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml" />
    <None Include="Assets/Resources/Text/content.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF SSD.shader" />
    <None Include="Assets/Packages/System.Net.Http.Json.9.0.1/lib/netstandard2.0/System.Net.Http.Json.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Caching.Abstractions.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Diagnostics.DiagnosticSource.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Photon/Fusion/Assemblies/Fusion.Common.dll" />
    <None Include="Assets/Packages/Microsoft.Bcl.AsyncInterfaces.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Photon/Fusion/Assemblies/Fusion.Runtime.xml" />
    <None Include="Assets/Photon/Fusion/Plugins/NanoSockets/NanoSockets/Static/NanoSockets.dll" />
    <None Include="Assets/Packages/System.ComponentModel.Annotations.5.0.0/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Diagnostics.DiagnosticSource.9.0.1/lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Primitives.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Primitives.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Caching.Memory.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/zh-Hans/Microsoft.Extensions.Options.SourceGeneration.resources.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets/Photon/Fusion/Assemblies/Fusion.Sockets.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.DependencyInjection.Abstractions.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/Microsoft.Extensions.DependencyInjection.Abstractions.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Http.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Http.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml" />
    <None Include="Assets/Transition_tessellation/Shader/TessellationRevealHologram.shader" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/System.Threading.Channels.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/SDFFunctions.hlsl" />
    <None Include="Assets/Packages/Microsoft.Extensions.Primitives.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Http.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Photon/Fusion/Assemblies/Fusion.Log.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Photon/Fusion/release_history.txt" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Script/ChatAgent/plan.txt" />
    <None Include="Assets/Script/_KioskExperiance/contents/ananda/Prompt_ananda_v2.txt" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Photon/Fusion/Plugins/NanoSockets/NanoSockets/Dynamic/NanoSockets.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/System.Threading.Channels.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Photon/Fusion/Assemblies/Fusion.Runtime.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Surface.shader" />
    <None Include="Assets/Packages/Microsoft.Bcl.TimeProvider.9.0.1/lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.ComponentModel.Annotations.5.0.0/LICENSE.TXT" />
    <None Include="Assets/Photon/Fusion/Plugins/NanoSockets/Metro/NanoSockets.dll" />
    <None Include="Assets/Packages/System.IO.Pipelines.9.0.1/lib/netstandard2.0/System.IO.Pipelines.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/TextMesh Pro/Sprites/EmojiOne Attribution.txt" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Caching.Abstractions.9.0.1/LICENSE.TXT" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets/Packages/System.Net.Http.Json.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/System.Net.Http.Json.9.0.1/LICENSE.TXT" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF.shader" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Primitives.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile SSD.shader" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.IO.Pipelines.9.0.1/lib/netstandard2.0/System.IO.Pipelines.dll" />
    <None Include="Assets/Photon/PhotonLibs/changes-library.txt" />
    <None Include="Assets/TextMesh Pro/Resources/LineBreaking Leading Characters.txt" />
    <None Include="Assets/Photon/Fusion/Assemblies/Fusion.Common.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Bcl.AsyncInterfaces.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.DependencyInjection.9.0.1/lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/TextMesh Pro/Shaders/TMPro_Properties.cginc" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Photon/Fusion/Plugins/NanoSockets/Metro/x64/libnanosockets.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.DependencyInjection.Abstractions.9.0.1/lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/TMPro_Surface.cginc" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Photon/Fusion/Plugins/NanoSockets/Metro/x86/libnanosockets.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.ComponentModel.Annotations.5.0.0/lib/netstandard2.1/System.ComponentModel.Annotations.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Primitives.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Primitives.xml" />
    <None Include="Assets/Photon/PhotonLibs/netstandard2.0/Photon3Unity3D.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Abstractions.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Net.Http.Json.9.0.1/lib/netstandard2.0/System.Net.Http.Json.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/PostHog.1.0.5/lib/netstandard2.1/PostHog.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/TextMesh Pro/Resources/LineBreaking Following Characters.txt" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Bcl.TimeProvider.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Photon/Fusion/Plugins/NanoSockets/Windows/nanosockets.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Http.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Http.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Bcl.TimeProvider.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Primitives.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/Microsoft.Bcl.TimeProvider.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Bcl.AsyncInterfaces.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/lib/netstandard2.0/System.Text.Json.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/lib/netstandard2.0/System.Text.Json.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets/Packages/Microsoft.Extensions.Caching.Memory.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/System.ComponentModel.Annotations.5.0.0/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/Microsoft.Extensions.DependencyInjection.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml" />
    <None Include="Assets/Packages/System.Text.Encodings.Web.9.0.1/lib/netstandard2.0/System.Text.Encodings.Web.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.ComponentModel.Annotations.5.0.0/lib/netstandard2.1/System.ComponentModel.Annotations.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hans/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Photon/Fusion/build_info.txt" />
    <None Include="Assets/Photon/Fusion/Assemblies/Fusion.Realtime.dll" />
    <None Include="Assets/Script/_KioskExperiance/contents/ananda/PromptBackups_ananda_v2.txt" />
    <None Include="Assets/Photon/Fusion/Plugins/NanoSockets/Metro/ARM64/libnanosockets.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Diagnostics.DiagnosticSource.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll" />
    <None Include="Assets/Photon/Fusion/Plugins/NanoSockets/WebGL/NanoSockets.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/zh-Hans/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF Overlay.shader" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.DependencyInjection.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_Bitmap-Mobile.shader" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Caching.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll" />
    <None Include="Assets/Photon/Fusion/Assemblies/Fusion.Sockets.xml" />
    <None Include="Assets/Packages/System.Diagnostics.DiagnosticSource.9.0.1/content/ILLink/ILLink.Descriptors.LibraryBuild.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/zh-Hans/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/TextMesh Pro/Fonts/LiberationSans - OFL.txt" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.9.0.1/lib/netstandard2.1/Microsoft.Extensions.Logging.xml" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Caching.Abstractions.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/Microsoft.Extensions.Http.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile.shader" />
    <None Include="Assets/Packages/Microsoft.Bcl.AsyncInterfaces.9.0.1/lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml" />
    <None Include="Assets/Packages/Microsoft.Bcl.TimeProvider.9.0.1/lib/netstandard2.0/Microsoft.Bcl.TimeProvider.xml" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.ComponentModel.Annotations.5.0.0/version.txt" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.DependencyInjection.Abstractions.9.0.1/lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll" />
    <None Include="Assets/Packages/System.Threading.Channels.9.0.1/lib/netstandard2.1/System.Threading.Channels.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.DependencyInjection.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/Microsoft.Bcl.AsyncInterfaces.9.0.1/lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll" />
    <None Include="Assets/Packages/System.Diagnostics.DiagnosticSource.9.0.1/lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml" />
    <None Include="Assets/Packages/Microsoft.Extensions.Caching.Memory.9.0.1/LICENSE.TXT" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/zh-Hans/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Configuration.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll" />
    <None Include="Assets/Packages/Microsoft.Extensions.Options.9.0.1/analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Photon/Fusion/Plugins/NanoSockets/Metro/ARM/libnanosockets.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_Sprite.shader" />
    <None Include="Assets/Packages/System.Text.Encodings.Web.9.0.1/useSharedDesignerContext.txt" />
    <None Include="Assets/Packages/Microsoft.Extensions.Logging.9.0.1/lib/netstandard2.1/Microsoft.Extensions.Logging.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options">
      <HintPath>Assets/Packages/Microsoft.Extensions.Options.9.0.1/lib/netstandard2.1/Microsoft.Extensions.Options.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder">
      <HintPath>Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encodings.Web">
      <HintPath>Assets/Packages/System.Text.Encodings.Web.9.0.1/lib/netstandard2.0/System.Text.Encodings.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions">
      <HintPath>Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection">
      <HintPath>Assets/Packages/Microsoft.Extensions.DependencyInjection.9.0.1/lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="websocket-sharp">
      <HintPath>Assets/Photon/PhotonLibs/WebSocket/websocket-sharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Fusion.Common">
      <HintPath>Assets/Photon/Fusion/Assemblies/Fusion.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NanoSockets">
      <HintPath>Assets/Photon/Fusion/Plugins/NanoSockets/NanoSockets/Dynamic/NanoSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource">
      <HintPath>Assets/Packages/System.Diagnostics.DiagnosticSource.9.0.1/lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives">
      <HintPath>Assets/Packages/Microsoft.Extensions.Primitives.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Memory">
      <HintPath>Assets/Packages/Microsoft.Extensions.Caching.Memory.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Fusion.Sockets">
      <HintPath>Assets/Photon/Fusion/Assemblies/Fusion.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Fusion.Log">
      <HintPath>Assets/Photon/Fusion/Assemblies/Fusion.Log.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Fusion.Runtime">
      <HintPath>Assets/Photon/Fusion/Assemblies/Fusion.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.TimeProvider">
      <HintPath>Assets/Packages/Microsoft.Bcl.TimeProvider.9.0.1/lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipelines">
      <HintPath>Assets/Packages/System.IO.Pipelines.9.0.1/lib/netstandard2.0/System.IO.Pipelines.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Photon3Unity3D">
      <HintPath>Assets/Photon/PhotonLibs/netstandard2.0/Photon3Unity3D.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Json">
      <HintPath>Assets/Packages/System.Net.Http.Json.9.0.1/lib/netstandard2.0/System.Net.Http.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PostHog">
      <HintPath>Assets/Packages/PostHog.1.0.5/lib/netstandard2.1/PostHog.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Http">
      <HintPath>Assets/Packages/Microsoft.Extensions.Http.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Json">
      <HintPath>Assets/Packages/System.Text.Json.9.0.1/lib/netstandard2.0/System.Text.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>Assets/Packages/System.ComponentModel.Annotations.5.0.0/lib/netstandard2.1/System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Fusion.Realtime">
      <HintPath>Assets/Photon/Fusion/Assemblies/Fusion.Realtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Abstractions">
      <HintPath>Assets/Packages/Microsoft.Extensions.Caching.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions">
      <HintPath>Assets/Packages/Microsoft.Extensions.DependencyInjection.Abstractions.9.0.1/lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Channels">
      <HintPath>Assets/Packages/System.Threading.Channels.9.0.1/lib/netstandard2.1/System.Threading.Channels.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Assets/Packages/Microsoft.Bcl.AsyncInterfaces.9.0.1/lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions">
      <HintPath>Assets/Packages/Microsoft.Extensions.Configuration.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging">
      <HintPath>Assets/Packages/Microsoft.Extensions.Logging.9.0.1/lib/netstandard2.1/Microsoft.Extensions.Logging.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Interaction.Toolkit.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.BuildingBlocks.DepthAPI.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.BuildingBlocks.DepthAPI.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Voice.Hub.Runtime">
      <HintPath>Library/ScriptAssemblies/Meta.Voice.Hub.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation">
      <HintPath>Library/ScriptAssemblies/Unity.AI.Navigation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.MultiplayerBlocks.Fusion.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.MultiplayerBlocks.Fusion.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.CoreUtils">
      <HintPath>Library/ScriptAssemblies/Unity.XR.CoreUtils.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.OVR">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.OVR.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="meta.xr.mrutilitykit.editor">
      <HintPath>Library/ScriptAssemblies/meta.xr.mrutilitykit.editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Platform.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.Platform.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.MultiplayerBlocks.Shared.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.MultiplayerBlocks.Shared.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi.TTS.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.TTS.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.BuildingBlocks">
      <HintPath>Library/ScriptAssemblies/Meta.XR.BuildingBlocks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="meta.xr.mrutilitykit">
      <HintPath>Library/ScriptAssemblies/meta.xr.mrutilitykit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library/ScriptAssemblies/Unity.Timeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ShaderGraph.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.ShaderLibrary">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Wit.Dictation">
      <HintPath>Library/ScriptAssemblies/Meta.Wit.Dictation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.VR.Scripts.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.VR.Scripts.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.GPUDriven.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Oculus">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Oculus.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Fbx">
      <HintPath>Library/ScriptAssemblies/Autodesk.Fbx.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PPv2URPConverters">
      <HintPath>Library/ScriptAssemblies/PPv2URPConverters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Telemetry">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Telemetry.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.OVR.Samples">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.OVR.Samples.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.VoiceSDK.Mic.Other">
      <HintPath>Library/ScriptAssemblies/Meta.VoiceSDK.Mic.Other.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi.Lib">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.Lib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Config.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.ImmersiveDebugger.Interface">
      <HintPath>Library/ScriptAssemblies/Meta.XR.ImmersiveDebugger.Interface.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.VoiceSDK.Mic.WebGL">
      <HintPath>Library/ScriptAssemblies/Meta.VoiceSDK.Mic.WebGL.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Collections.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="CesiumEditor">
      <HintPath>Library/ScriptAssemblies/CesiumEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpatialTracking">
      <HintPath>Library/ScriptAssemblies/UnityEngine.SpatialTracking.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Updater">
      <HintPath>Library/ScriptAssemblies/Unity.AI.Navigation.Updater.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Editor.PlayCompanion">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Editor.PlayCompanion.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Editor.Tags">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Editor.Tags.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Platform">
      <HintPath>Library/ScriptAssemblies/Oculus.Platform.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Fbx.Editor">
      <HintPath>Library/ScriptAssemblies/Autodesk.Fbx.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.VoiceSDK.Mic.Common">
      <HintPath>Library/ScriptAssemblies/Meta.VoiceSDK.Mic.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library/ScriptAssemblies/Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Management">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Management.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Runtime.Composer">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Runtime.Composer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AssistantCoreSDKEditor">
      <HintPath>Library/ScriptAssemblies/AssistantCoreSDKEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MetaXrSimulator.Editor">
      <HintPath>Library/ScriptAssemblies/MetaXrSimulator.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Oculus.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Oculus.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.ImmersiveDebugger.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.ImmersiveDebugger.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AssistantVoiceCommandCommon">
      <HintPath>Library/ScriptAssemblies/AssistantVoiceCommandCommon.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Guides.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Guides.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor.ConversionSystem">
      <HintPath>Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Voice.Hub.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.Voice.Hub.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Haptics.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.Haptics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Wit.Composer.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.Wit.Composer.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.OVR.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.OVR.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Audio.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Audio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="CesiumRuntime">
      <HintPath>Library/ScriptAssemblies/CesiumRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.ImmersiveDebugger">
      <HintPath>Library/ScriptAssemblies/Meta.XR.ImmersiveDebugger.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.WebRTC">
      <HintPath>Library/ScriptAssemblies/Unity.WebRTC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.InterfaceSupport">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.InterfaceSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.CoreUtils.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.CoreUtils.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpatialTracking">
      <HintPath>Library/ScriptAssemblies/UnityEditor.SpatialTracking.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.MultiplayerBlocks.Shared">
      <HintPath>Library/ScriptAssemblies/Meta.XR.MultiplayerBlocks.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Wit.Dictation.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.Wit.Dictation.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.2D.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.BuildingBlocks.DepthAPI">
      <HintPath>Library/ScriptAssemblies/Meta.XR.BuildingBlocks.DepthAPI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem.ForUI">
      <HintPath>Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library/ScriptAssemblies/UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Searcher.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Searcher.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.EnvironmentDepth">
      <HintPath>Library/ScriptAssemblies/Meta.XR.EnvironmentDepth.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.VR.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.VR.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Net.endel.nativewebsocket">
      <HintPath>Library/ScriptAssemblies/Meta.Net.endel.nativewebsocket.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipeline.Universal.ShaderLibrary">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Shaders">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.VR">
      <HintPath>Library/ScriptAssemblies/Oculus.VR.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi.Conduit">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.Conduit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime.Shared">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Timeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Editor.StatusMenu">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Editor.StatusMenu.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Wit.Composer">
      <HintPath>Library/ScriptAssemblies/Meta.Wit.Composer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor.Shared">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library/ScriptAssemblies/UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi.TTS">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.TTS.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.MultiplayerBlocks.Fusion">
      <HintPath>Library/ScriptAssemblies/Meta.XR.MultiplayerBlocks.Fusion.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAI.Lib.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.WitAI.Lib.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Mathematics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AssistantVoiceCommandCommon.Editor">
      <HintPath>Library/ScriptAssemblies/AssistantVoiceCommandCommon.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Editor.Callbacks">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Editor.Callbacks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Haptics">
      <HintPath>Library/ScriptAssemblies/Oculus.Haptics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XR.LegacyInputHelpers">
      <HintPath>Library/ScriptAssemblies/UnityEditor.XR.LegacyInputHelpers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.WebRTC.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.WebRTC.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections">
      <HintPath>Library/ScriptAssemblies/Unity.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.EnvironmentDepth.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.EnvironmentDepth.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Management.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Management.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XR.LegacyInputHelpers">
      <HintPath>Library/ScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.Voice.VSDKHub.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.Voice.VSDKHub.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.Samples">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.Samples.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Editor">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Oculus.Interaction.Samples.Editor">
      <HintPath>Library/ScriptAssemblies/Oculus.Interaction.Samples.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi.Conduit.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.Conduit.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.MultiplayerBlocks.NGO.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.MultiplayerBlocks.NGO.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Common">
      <HintPath>Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.BuildingBlocks.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.XR.BuildingBlocks.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Dictation.Runtime">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Dictation.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Interaction.Toolkit">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.EditorCoroutines.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NuGetForUnity">
      <HintPath>Library/ScriptAssemblies/NuGetForUnity.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.AI.Navigation.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics">
      <HintPath>Library/ScriptAssemblies/Unity.Mathematics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Editor.UserInterface">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Editor.UserInterface.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Interaction.Toolkit.Analytics.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.Analytics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Editor.Composer">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Editor.Composer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Formats.Fbx.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.Formats.Fbx.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.WitAi.Editor">
      <HintPath>Library/ScriptAssemblies/Meta.WitAi.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Runtime">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Burst.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Formats.Fbx.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Formats.Fbx.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>Library/ScriptAssemblies/Unity.Burst.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Meta.XR.Audio">
      <HintPath>Library/ScriptAssemblies/Meta.XR.Audio.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AssistantCoreSDKRuntime">
      <HintPath>Library/ScriptAssemblies/AssistantCoreSDKRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="VoiceSDK.Dictation.Editor">
      <HintPath>Library/ScriptAssemblies/VoiceSDK.Dictation.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>Library/ScriptAssemblies/Unity.InputSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp-firstpass.csproj" />
    <ProjectReference Include="Fusion.Unity.csproj" />
    <ProjectReference Include="BakeryRuntimeAssembly.csproj" />
    <ProjectReference Include="Cognitive3D.csproj" />
    <ProjectReference Include="BakeryEditorAssembly.csproj" />
    <ProjectReference Include="Fusion.Unity.Editor.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
