using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

public class CoverBlockGenerator : EditorWindow
{
    private int subdivisionLevel = 1;
    private float gapOffset = 0.1f;
    private bool enableDeformation = true;
    private float coverageThreshold = 0.95f;
    private int maxIterations = 10;
    private Material userMaterial = null;
    private float uvScale = 1.0f;

    [MenuItem("Tools/Generate Cover Block")]
    public static void ShowWindow()
    {
        GetWindow<CoverBlockGenerator>("Cover Block Generator");
    }

    void OnGUI()
    {
        GUILayout.Label("Bounding Box Settings", EditorStyles.boldLabel);
        subdivisionLevel = EditorGUILayout.IntSlider("Subdivision Level", subdivisionLevel, 1, 20);
        gapOffset = EditorGUILayout.Slider("Gap Offset", gapOffset, 0.01f, 1.0f);
        enableDeformation = EditorGUILayout.Toggle("Enable Deformation", enableDeformation);
        
        // Material selection
        userMaterial = (Material)EditorGUILayout.ObjectField("Cover Material", userMaterial, typeof(Material), false);
        
        // UV Scale field
        uvScale = EditorGUILayout.FloatField("UV Scale", uvScale);
        
        if (enableDeformation)
        {
            GUILayout.Space(10);
            GUILayout.Label("Deformation Settings", EditorStyles.boldLabel);
            coverageThreshold = EditorGUILayout.Slider("Coverage Threshold", coverageThreshold, 0.8f, 1.0f);
            maxIterations = EditorGUILayout.IntSlider("Max Iterations", maxIterations, 5, 20);
        }

        if (GUILayout.Button("Generate Cover block"))
        {
            GenerateCoverBlocksForSelection(subdivisionLevel, gapOffset, enableDeformation, coverageThreshold, maxIterations, userMaterial, uvScale);
        }
    }

    static void GenerateCoverBlocksForSelection(int subdivisions, float gap, bool deform, float coverageThreshold, int maxIterations, Material userMaterial, float uvScale)
    {
        var selectedObjects = Selection.gameObjects;
        if (selectedObjects == null || selectedObjects.Length == 0)
        {
            Debug.LogError("❌ Please select at least one GameObject!");
            return;
        }

        int totalProcessed = 0;
        foreach (var obj in selectedObjects)
        {
            bool found = false;
            // If the object has a MeshFilter, process it
            MeshFilter mf = obj.GetComponent<MeshFilter>();
            if (mf != null && mf.sharedMesh != null)
            {
                CreateBoundingBoxForObject(obj, subdivisions, gap, deform, coverageThreshold, maxIterations, userMaterial, uvScale);
                found = true;
                totalProcessed++;
            }
            else
            {
                // Otherwise, search children for MeshFilters
                var meshFilters = obj.GetComponentsInChildren<MeshFilter>();
                foreach (var childMF in meshFilters)
                {
                    if (childMF.sharedMesh != null)
                    {
                        CreateBoundingBoxForObject(childMF.gameObject, subdivisions, gap, deform, coverageThreshold, maxIterations, userMaterial, uvScale);
                        found = true;
                        totalProcessed++;
                    }
                }
            }
            if (!found)
            {
                Debug.LogWarning($"⚠️ No MeshFilter found on '{obj.name}' or its children.");
            }
        }
        if (totalProcessed == 0)
        {
            Debug.LogError("❌ No valid meshes found in selection.");
        }
    }

    static void CreateBoundingBoxForObject(GameObject selected, int subdivisions, float gap, bool deform, float coverageThreshold, int maxIterations, Material userMaterial, float uvScale)
    {
        if (selected == null || selected.GetComponent<MeshFilter>() == null)
        {
            Debug.LogError($"❌ GameObject '{selected?.name}' does not have a MeshFilter!");
            return;
        }

        Mesh mesh = selected.GetComponent<MeshFilter>().sharedMesh;
        if (mesh == null || mesh.vertexCount == 0)
        {
            Debug.LogError($"❌ Mesh not found or empty on '{selected.name}'.");
            return;
        }

        Transform t = selected.transform;
        Vector3[] verts = mesh.vertices;

        Vector3 min = t.TransformPoint(verts[0]);
        Vector3 max = min;
        foreach (Vector3 v in verts)
        {
            Vector3 worldV = t.TransformPoint(v);
            min = Vector3.Min(min, worldV);
            max = Vector3.Max(max, worldV);
        }

        Vector3 size = max - min;
        Vector3 center = (min + max) * 0.5f;

        // Create initial cube
        Mesh initialCube = CreateSubdividedCube(size + Vector3.one * gap * 2, subdivisions);
        
        // Deform the cube if enabled
        Mesh finalMesh;
        if (deform && subdivisions > 1)
        {
            finalMesh = ShrinkwrapDeformMesh(initialCube, mesh, t, center, gap);
        }
        else if (deform && subdivisions == 1)
        {
            finalMesh = initialCube; // No deformation for subdivision 1
        }
        else
        {
            finalMesh = initialCube;
        }

        // After mesh deformation and before assigning to MeshFilter
        UnwrapParam param = new UnwrapParam();
        UnwrapParam.SetDefaults(out param);
        Unwrapping.GenerateSecondaryUVSet(finalMesh, param);
        finalMesh.uv = finalMesh.uv2; // Use the generated UVs as the main UVs
        // Apply UV scale
        if (Mathf.Abs(uvScale - 1.0f) > 0.0001f)
        {
            Vector2[] uvs = finalMesh.uv;
            for (int i = 0; i < uvs.Length; i++)
            {
                uvs[i] *= uvScale;
            }
            finalMesh.uv = uvs;
        }

#if UNITY_EDITOR
        // Save the mesh as a persistent asset
        string meshFolder = "Assets/GeneratedCovers";
        if (!AssetDatabase.IsValidFolder(meshFolder))
        {
            AssetDatabase.CreateFolder("Assets", "GeneratedCovers");
        }
        string meshPath = $"{meshFolder}/{selected.name}_Cover_Mesh.asset";
        // Ensure unique asset name
        meshPath = AssetDatabase.GenerateUniqueAssetPath(meshPath);
        AssetDatabase.CreateAsset(finalMesh, meshPath);
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        Debug.Log($"[CoverBlockGenerator] Saved mesh asset to: {meshPath}");
#endif

        GameObject cubeObj = new GameObject(selected.name + "_Cover");
        cubeObj.transform.position = center;
        cubeObj.transform.SetParent(selected.transform.parent);

        // Ensure "Cover_Block" exists and parent the new mesh under it
        GameObject coverBlock = GameObject.Find("Cover_Block");
        if (coverBlock == null)
        {
            coverBlock = new GameObject("Cover_Block");
        }
        cubeObj.transform.SetParent(coverBlock.transform, true); // keep world position

        MeshFilter mf = cubeObj.AddComponent<MeshFilter>();
#if UNITY_EDITOR
        // Assign the saved mesh asset to the MeshFilter
        Mesh savedMesh = AssetDatabase.LoadAssetAtPath<Mesh>(meshPath);
        mf.sharedMesh = savedMesh != null ? savedMesh : finalMesh;
#else
        mf.sharedMesh = finalMesh;
#endif
        MeshRenderer mr = cubeObj.AddComponent<MeshRenderer>();

        // Assign user-selected material, or fallback to default
        if (userMaterial != null)
        {
            mr.sharedMaterial = userMaterial;
        }
        else
        {
            // Try to load the default material by name
            Material defaultMat = Resources.Load<Material>("TessellationRevealMat 1");
            if (defaultMat == null)
            {
                // Try to find it among all loaded materials in the project
                var allMats = Resources.FindObjectsOfTypeAll<Material>();
                foreach (var mat in allMats)
                {
                    if (mat.name == "TessellationRevealMat 1")
                    {
                        defaultMat = mat;
                        break;
                    }
                }
            }
            if (defaultMat != null)
            {
                mr.sharedMaterial = defaultMat;
            }
            else
            {
                // Fallback to standard transparent red
                mr.sharedMaterial = new Material(Shader.Find("Standard"))
                {
                    color = new Color(1f, 0f, 0f, 0.3f)
                };
            }
        }

        Selection.activeGameObject = cubeObj;
        Debug.Log($"✅ Bounding box with subdivision level {subdivisions} created for '{selected.name}'.");
    }

    // --- SHRINKWRAP DEFORMATION ---
    static Mesh ShrinkwrapDeformMesh(Mesh cubeMesh, Mesh targetMesh, Transform targetTransform, Vector3 center, float gap)
    {
        Vector3[] cubeVertices = cubeMesh.vertices;
        Vector3[] deformedVertices = new Vector3[cubeVertices.Length];

        // Prepare target mesh data in world space
        Vector3[] targetVerts = targetMesh.vertices;
        int[] targetTris = targetMesh.triangles;
        List<Vector3> targetWorldVerts = new List<Vector3>();
        foreach (Vector3 vert in targetVerts)
        {
            targetWorldVerts.Add(targetTransform.TransformPoint(vert));
        }

        // For each vertex in the cube, project to closest point on mesh, offset by gap
        for (int i = 0; i < cubeVertices.Length; i++)
        {
            Vector3 worldVertex = cubeVertices[i] + center;
            Vector3 closestPoint = FindClosestPointOnMesh(worldVertex, targetWorldVerts, targetTris, targetTransform);
            Vector3 offsetDir = (worldVertex - closestPoint).normalized;
            if (offsetDir == Vector3.zero)
                offsetDir = (worldVertex - center).normalized; // fallback if coincident
            deformedVertices[i] = (closestPoint + offsetDir * gap) - center;
        }

        Mesh deformedMesh = new Mesh();
        deformedMesh.vertices = deformedVertices;
        deformedMesh.triangles = cubeMesh.triangles;
        deformedMesh.uv = cubeMesh.uv;
        deformedMesh.RecalculateNormals();
        deformedMesh.RecalculateBounds();
        Debug.Log("🎉 Shrinkwrap deformation complete! Final mesh created.");
        return deformedMesh;
    }

    static Vector3 FindOptimalVertexPosition(Vector3 currentPos, Vector3 originalPos, List<Vector3> targetVerts, int[] targetTris, Transform targetTransform, float gap)
    {
        Vector3 bestPos = currentPos;
        float bestScore = float.MinValue;
        
        // Try multiple directions to find optimal position
        Vector3[] directions = {
            Vector3.up, Vector3.down, Vector3.left, Vector3.right, Vector3.forward, Vector3.back,
            (Vector3.up + Vector3.left).normalized, (Vector3.up + Vector3.right).normalized,
            (Vector3.down + Vector3.left).normalized, (Vector3.down + Vector3.right).normalized,
            (Vector3.forward + Vector3.up).normalized, (Vector3.back + Vector3.up).normalized
        };
        
        foreach (Vector3 direction in directions)
        {
            // Cast ray in this direction
            Ray ray = new Ray(currentPos, direction);
            float maxDistance = 5f;
            
            if (Physics.Raycast(ray, out RaycastHit hit, maxDistance))
            {
                Vector3 candidatePos = hit.point + hit.normal * gap;
                float score = EvaluateVertexPosition(candidatePos, originalPos, targetVerts, targetTris, targetTransform);
                
                if (score > bestScore)
                {
                    bestScore = score;
                    bestPos = candidatePos;
                }
            }
        }
        
        // Also try closest point approach
        Vector3 closestPoint = FindClosestPointOnMesh(currentPos, targetVerts, targetTris, targetTransform);
        Vector3 closestCandidate = closestPoint + (currentPos - closestPoint).normalized * gap;
        float closestScore = EvaluateVertexPosition(closestCandidate, originalPos, targetVerts, targetTris, targetTransform);
        
        if (closestScore > bestScore)
        {
            bestPos = closestCandidate;
        }
        
        return bestPos;
    }

    static float EvaluateVertexPosition(Vector3 pos, Vector3 originalPos, List<Vector3> targetVerts, int[] targetTris, Transform targetTransform)
    {
        float score = 0f;
        
        // Distance from original position (less penalty for moving)
        float distanceFromOriginal = Vector3.Distance(pos, originalPos);
        score -= distanceFromOriginal * 0.05f; // Reduced penalty
        
        // Coverage contribution (more aggressive)
        int coveredPoints = 0;
        foreach (Vector3 targetVert in targetVerts)
        {
            if (Vector3.Distance(pos, targetVert) <= 3f) // Larger coverage radius
            {
                coveredPoints++;
            }
        }
        score += coveredPoints * 1.0f; // Increased reward
        
        // Prefer positions closer to target mesh
        float closestDistance = float.MaxValue;
        foreach (Vector3 targetVert in targetVerts)
        {
            float dist = Vector3.Distance(pos, targetVert);
            if (dist < closestDistance)
            {
                closestDistance = dist;
            }
        }
        score += (10f - closestDistance) * 0.5f; // Reward for being close to target
        
        return score;
    }

    static Vector3 FindClosestPointOnMesh(Vector3 point, List<Vector3> targetVerts, int[] targetTris, Transform targetTransform)
    {
        float closestDistance = float.MaxValue;
        Vector3 closestPoint = point;
        
        // Check each triangle
        for (int i = 0; i < targetTris.Length; i += 3)
        {
            Vector3 v1 = targetVerts[targetTris[i]];
            Vector3 v2 = targetVerts[targetTris[i + 1]];
            Vector3 v3 = targetVerts[targetTris[i + 2]];
            
            Vector3 triangleClosest = ClosestPointOnTriangle(point, v1, v2, v3);
            float distance = Vector3.Distance(point, triangleClosest);
            
            if (distance < closestDistance)
            {
                closestDistance = distance;
                closestPoint = triangleClosest;
            }
        }
        
        return closestPoint;
    }

    static Vector3 ClosestPointOnTriangle(Vector3 point, Vector3 a, Vector3 b, Vector3 c)
    {
        Vector3 ab = b - a;
        Vector3 ac = c - a;
        Vector3 ap = point - a;

        float d1 = Vector3.Dot(ab, ap);
        float d2 = Vector3.Dot(ac, ap);

        if (d1 <= 0.0f && d2 <= 0.0f)
            return a;

        Vector3 bp = point - b;
        float d3 = Vector3.Dot(ab, bp);
        float d4 = Vector3.Dot(ac, bp);

        if (d3 >= 0.0f && d4 <= d3)
            return b;

        Vector3 cp = point - c;
        float d5 = Vector3.Dot(ab, cp);
        float d6 = Vector3.Dot(ac, cp);

        if (d6 >= 0.0f && d5 <= d6)
            return c;

        float vc = d1 * d4 - d3 * d2;
        if (vc <= 0.0f && d1 >= 0.0f && d3 <= 0.0f)
        {
            float v = d1 / (d1 - d3);
            return a + v * ab;
        }

        float vb = d5 * d2 - d1 * d6;
        if (vb <= 0.0f && d2 >= 0.0f && d6 <= 0.0f)
        {
            float v = d2 / (d2 - d6);
            return a + v * ac;
        }

        float va = d3 * d6 - d5 * d4;
        if (va <= 0.0f && (d4 - d3) >= 0.0f && (d5 - d6) >= 0.0f)
        {
            float v = (d4 - d3) / ((d4 - d3) + (d5 - d6));
            return b + v * (c - b);
        }

        float denom = 1.0f / (va + vb + vc);
        float v2 = vb * denom;
        float w = vc * denom;
        return a + v2 * ab + w * ac;
    }

    static float CalculateCoverage(Vector3[] coverVertices, Vector3 center, List<Vector3> targetVertices)
    {
        int coveredPoints = 0;
        float coverageRadius = 1f; // Adjust based on your needs
        
        foreach (Vector3 targetVert in targetVertices)
        {
            bool isCovered = false;
            
            foreach (Vector3 coverVert in coverVertices)
            {
                Vector3 worldCoverVert = coverVert + center;
                if (Vector3.Distance(worldCoverVert, targetVert) <= coverageRadius)
                {
                    isCovered = true;
                    break;
                }
            }
            
            if (isCovered)
                coveredPoints++;
        }
        
        return (float)coveredPoints / targetVertices.Count;
    }

    static Mesh CreateSubdividedCube(Vector3 size, int subdivisions)
    {
        Mesh mesh = new Mesh();
        mesh.name = "SubdividedCube";

        // 6 faces × quads per face
        int quadsPerFace = subdivisions * subdivisions;
        int totalFaces = 6;
        int quadCount = totalFaces * quadsPerFace;
        int vertPerFace = (subdivisions + 1) * (subdivisions + 1);

        Vector3[] vertices = new Vector3[vertPerFace * totalFaces];
        Vector2[] uvs = new Vector2[vertices.Length];
        int[] triangles = new int[quadCount * 6];

        Vector3[] faceNormals = {
            Vector3.forward, Vector3.back,
            Vector3.left, Vector3.right,
            Vector3.up, Vector3.down
        };

        Vector3[] faceUps = {
            Vector3.up, Vector3.up,
            Vector3.forward, Vector3.forward,
            Vector3.forward, Vector3.forward
        };

        int vIndex = 0;
        int tIndex = 0;

        for (int face = 0; face < 6; face++)
        {
            Vector3 normal = faceNormals[face];
            Vector3 up = faceUps[face];
            Vector3 right = Vector3.Cross(up, normal);

            for (int y = 0; y <= subdivisions; y++)
            {
                for (int x = 0; x <= subdivisions; x++)
                {
                    float u = x / (float)subdivisions;
                    float v = y / (float)subdivisions;

                    Vector3 localPos = normal * 0.5f +
                                       (right * (u - 0.5f)) +
                                       (up * (v - 0.5f));

                    vertices[vIndex] = Vector3.Scale(localPos, size);
                    uvs[vIndex] = new Vector2(u, v);
                    vIndex++;
                }
            }

            int startVert = face * vertPerFace;
            for (int y = 0; y < subdivisions; y++)
            {
                for (int x = 0; x < subdivisions; x++)
                {
                    int i0 = startVert + y * (subdivisions + 1) + x;
                    int i1 = i0 + 1;
                    int i2 = i0 + (subdivisions + 1);
                    int i3 = i2 + 1;

                    // First triangle (clockwise winding for proper normals)
                    triangles[tIndex++] = i0;
                    triangles[tIndex++] = i1;
                    triangles[tIndex++] = i2;

                    // Second triangle (clockwise winding for proper normals)
                    triangles[tIndex++] = i2;
                    triangles[tIndex++] = i1;
                    triangles[tIndex++] = i3;
                }
            }
        }

        mesh.vertices = vertices;
        mesh.triangles = triangles;
        mesh.uv = uvs;
        mesh.RecalculateNormals();
        mesh.RecalculateBounds();

        return mesh;
    }
}