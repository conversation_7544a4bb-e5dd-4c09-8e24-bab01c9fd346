%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Pillow_Big_LOD0_Cover_Mesh
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.6899867, y: 0.074148715, z: 0.3813205}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200020001000300040005000600060005000700080009000a000a0009000b000c000d000e000e000d000f00100011001200120011001300140015001600160015001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 32
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 960
    _typelessdata: f8a230bf48db97bd713cc33e00000000000000000000803f8203803c6d1933408203803b6d19333ff8a2303f48db97bd713cc33e00000000000000000000803fab5220406d193340ab52203f6d19333ff8a230bf48db973d713cc33e00000000000000000000803f0000803c863844400000803b8638443ff8a2303f48db973d713cc33e00000000000000000000803fa452204086384440a452203f8638443ff8a2303f48db97bd713cc3be0000000000000000000080bf8203803c863845408203803b8638453ff8a230bf48db97bd713cc3be0000000000000000000080bfab52204086384540ab52203f8638453ff8a2303f48db973d713cc3be0000000000000000000080bf0000803c9f5756400000803b9f57563ff8a230bf48db973d713cc3be0000000000000000000080bfa45220409f575640a452203f9f57563ff8a230bf48db973d713cc3be000080bf0000000000000000ab522140c1f8903eab52213fc1f8903df8a230bf48db97bd713cc3be000080bf0000000000000000ab5221400000803cab52213f0000803bf8a230bf48db973d713cc33e000080bf0000000000000000635f7940c1f8903e635f793fc1f8903df8a230bf48db97bd713cc33e000080bf0000000000000000625f79400000803c625f793f0000803bf8a2303f48db97bd713cc3be0000803f0000000000000000625f7940c1f8983e625f793fc1f8983df8a2303f48db973d713cc3be0000803f0000000000000000635f7940c1f8103f635f793fc1f8103ef8a2303f48db97bd713cc33e0000803f0000000000000000ab522140c1f8983eab52213fc1f8983df8a2303f48db973d713cc33e0000803f0000000000000000ab522140c1f8103fab52213fc1f8103ef8a2303f48db973d713cc3be000000000000803f00000000a203803c6d19b43fa203803b6d19b43ef8a230bf48db973d713cc3be000000000000803f00000000ab5220406d19b43fab52203f6d19b43ef8a2303f48db973d713cc33e000000000000803f000000000000803c6d1932400000803b6d19323ff8a230bf48db973d713cc33e000000000000803f00000000a45220406d193240a452203f6d19323ff8a230bf48db97bd713cc3be00000000000080bf00000000a203803c0000803ca203803b0000803bf8a2303f48db97bd713cc3be00000000000080bf00000000ab5220400000803cab52203f0000803bf8a230bf48db97bd713cc33e00000000000080bf000000000000803c6d19b23f0000803b6d19b23ef8a2303f48db97bd713cc33e00000000000080bf00000000a45220406d19b23fa452203f6d19b23e
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.6899867, y: 0.074148715, z: 0.3813205}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
