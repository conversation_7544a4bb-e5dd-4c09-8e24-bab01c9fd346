%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Pillow_Small_LOD0_Cover_Mesh
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.3478637, y: 0.074255064, z: 0.3513699}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200020001000300040005000600060005000700080009000a000a0009000b000c000d000e000e000d000f00100011001200120011001300140015001600160015001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 32
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 960
    _typelessdata: 311bb2be0a1398bdc1e6b33e00000000000000000000803f4103803cb5f41a404103803bb5f41a3f311bb23e0a1398bdc1e6b33e00000000000000000000803f5e73fc3fb5f41a405e73fc3eb5f41a3f311bb2be0a13983dc1e6b33e00000000000000000000803f0000803cbfaf35400000803bbfaf353f311bb23e0a13983dc1e6b33e00000000000000000000803f5173fc3fbfaf35405173fc3ebfaf353f311bb23e0a1398bdc1e6b3be0000000000000000000080bf4103803cbfaf36404103803bbfaf363f311bb2be0a1398bdc1e6b3be0000000000000000000080bf5e73fc3fbfaf36405e73fc3ebfaf363f311bb23e0a13983dc1e6b3be0000000000000000000080bf0000803cca6a51400000803bca6a513f311bb2be0a13983dc1e6b3be0000000000000000000080bf5173fc3fca6a51405173fc3eca6a513f311bb2be0a13983dc1e6b3be000080bf0000000000000000cd7c004048d8dd3ecd7c003f48d8dd3d311bb2be0a1398bdc1e6b3be000080bf0000000000000000cd7c00400000803ccd7c003f0000803b311bb2be0a13983dc1e6b33e000080bf00000000000000009af97e4048d8dd3e9af97e3f48d8dd3d311bb2be0a1398bdc1e6b33e000080bf000000000000000099f97e400000803c99f97e3f0000803b311bb23e0a1398bdc1e6b3be0000803f000000000000000099f9fe3f0000803c99f9fe3e0000803b311bb23e0a13983dc1e6b3be0000803f00000000000000009af9fe3f48d8dd3e9af9fe3e48d8dd3d311bb23e0a1398bdc1e6b33e0000803f00000000000000000000803c0000803c0000803b0000803b311bb23e0a13983dc1e6b33e0000803f00000000000000002900803c48d8dd3e2900803b48d8dd3d311bb23e0a13983dc1e6b3be00000000ffff7f3f00000000c97c0040b5f41940c97c003fb5f4193f311bb2be0a13983dc1e6b3be00000000ffff7f3f00000000c97c004048d8e53ec97c003f48d8e53d311bb23e0a13983dc1e6b33e00000000ffff7f3f0000000093f97e40b5f4194093f97e3fb5f4193f311bb2be0a13983dc1e6b33e00000000ffff7f3f0000000093f97e4049d8e53e93f97e3f49d8e53d311bb2be0a1398bdc1e6b3be00000000ffff7fbf0000000092f9fe3f48d8e53e92f9fe3e48d8e53d311bb23e0a1398bdc1e6b3be00000000ffff7fbf0000000093f9fe3fb5f4194093f9fe3eb5f4193f311bb2be0a1398bdc1e6b33e00000000ffff7fbf000000000000803c48d8e53e0000803b48d8e53d311bb23e0a1398bdc1e6b33e00000000ffff7fbf000000000e00803cb5f419400e00803bb5f4193f
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.3478637, y: 0.074255064, z: 0.3513699}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
