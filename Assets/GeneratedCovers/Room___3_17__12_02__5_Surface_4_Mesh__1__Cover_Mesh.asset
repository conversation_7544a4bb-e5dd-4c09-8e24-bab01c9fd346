%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Room___3_17__12_02__5_Surface_4_Mesh__1__Cover_Mesh
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.03, y: 1.4649999, z: 1.53}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200020001000300040005000600060005000700080009000a000a0009000b000c000d000e000e000d000f00100011001200120011001300140015001600160015001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 32
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 960
    _typelessdata: 8fc2f5bc1e85bbbf0ad7c33f00000000000000000000803fffc17d406fb2003effc17d3f6fb2003d8fc2f53c1e85bbbf0ad7c33f00000000000000000000803fffc17d40a80b293effc17d3fa80b293d8fc2f5bc1e85bb3f0ad7c33f00000000000000000000803f619c02406fb2003e619c023f6fb2003d8fc2f53c1e85bb3f0ad7c33f00000000000000000000803f619c0240a80b293e619c023fa80b293d8fc2f53c1e85bbbf0ad7c3bf0000000000000000000080bf619c0240e064613e619c023fe064613d8fc2f5bc1e85bbbf0ad7c3bf0000000000000000000080bf619c0240a80b393e619c023fa80b393d8fc2f53c1e85bb3f0ad7c3bf0000000000000000000080bfffc17d40e064613effc17d3fe064613d8fc2f5bc1e85bb3f0ad7c3bf0000000000000000000080bfffc17d40a80b393effc17d3fa80b393d8fc2f5bc1e85bb3f0ad7c3bf000080bf00000000000000001a00803cc83003401a00803bc830033f8fc2f5bc1e85bbbf0ad7c3bf000080bf00000000000000000000803c6fb2003e0000803b6fb2003d8fc2f5bc1e85bb3f0ad7c33f000080bf0000000000000000619c0140c8300340619c013fc830033f8fc2f5bc1e85bbbf0ad7c33f000080bf0000000000000000609c01406fb2003e609c013f6fb2003d8fc2f53c1e85bbbf0ad7c3bf0000803f0000000000000000609c0140c8300440609c013fc830043f8fc2f53c1e85bb3f0ad7c3bf0000803f0000000000000000619c014068567f40619c013f68567f3f8fc2f53c1e85bbbf0ad7c33f0000803f00000000000000000000803cc83004400000803bc830043f8fc2f53c1e85bb3f0ad7c33f0000803f00000000000000001a00803c68567f401a00803b68567f3f8fc2f53c1e85bb3f0ad7c3bf000000000000803f000000000d00803cdf64613d0d00803bdf64613c8fc2f5bc1e85bb3f0ad7c3bf000000000000803f000000000000803c0400803c0000803b0400803b8fc2f53c1e85bb3f0ad7c33f000000000000803f00000000619c0140dd64613d619c013fdd64613c8fc2f5bc1e85bb3f0ad7c33f000000000000803f00000000619c01400000803c619c013f0000803b8fc2f5bc1e85bbbf0ad7c3bf00000000000080bf00000000619c014070b2903d619c013f70b2903c8fc2f53c1e85bbbf0ad7c3bf00000000000080bf00000000619c0140df64e13d619c013fdf64e13c8fc2f5bc1e85bbbf0ad7c33f00000000000080bf000000000000803c6fb2903d0000803b6fb2903c8fc2f53c1e85bbbf0ad7c33f00000000000080bf000000000c00803cde64e13d0c00803bde64e13c
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.03, y: 1.4649999, z: 1.53}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
