%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Round_Column_3_Cover_Mesh
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 144
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 59
    localAABB:
      m_Center: {x: 0, y: 0, z: -0.00000047683716}
      m_Extent: {x: 0.37400246, y: 3.9080288, z: 0.37400198}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 00000100020002000100030001000400030003000400050002000300060006000300070003000500070007000500080009000a000b000b000a000c000a000d000c000c000d000e000b000c000f000f000c0010000c000e00100010000e001100120013001400140013001500130016001500150016001700140015001800180015001900150017001900190017001a001b001c001d001d001c001e001c001f001e001e001f0020001d001e00210021001e0022001e0020002200220020002300240025002600260025002700250028002700270028002900260027002a002a0027002b00270029002b002b0029002c002d002e002f002f002e0030002e00310030003000310032002f0030003300330030003400350036003700380039003a00
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 59
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 32
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 2360
    _typelessdata: 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
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: -0.00000047683716}
    m_Extent: {x: 0.37400246, y: 3.9080288, z: 0.37400198}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
