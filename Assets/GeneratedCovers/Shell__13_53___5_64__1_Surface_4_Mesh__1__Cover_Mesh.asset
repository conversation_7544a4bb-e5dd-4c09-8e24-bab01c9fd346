%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Shell__13_53___5_64__1_Surface_4_Mesh__1__Cover_Mesh
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.02, y: 2.925, z: 1.2109563}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200020001000300040005000600060005000700080009000a000a0009000b000c000d000e000e000d000f00100011001200120011001300140015001600160015001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 32
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 960
    _typelessdata: 0ad7a3bc33333bc09e009b3f00000000000000000000803ff1037f3f7153553ff1037f3f7153553f0ad7a33c33333bc09e009b3f00000000000000000000803ff1037f3f1410573ff1037f3f1410573f0ad7a3bc33333b409e009b3f00000000000000000000803f0000803b7153553f0000803b7153553f0ad7a33c33333b409e009b3f00000000000000000000803f0000803b1410573f0000803b1410573f0ad7a33c33333bc09e009bbf0000000000000000000080bf0000803bb7cc593f0000803bb7cc593f0ad7a3bc33333bc09e009bbf0000000000000000000080bf0000803b1410583f0000803b1410583f0ad7a33c33333b409e009bbf0000000000000000000080bff1037f3fb7cc593ff1037f3fb7cc593f0ad7a3bc33333b409e009bbf0000000000000000000080bff1037f3f1410583ff1037f3f1410583f0ad7a3bc33333b409e009bbf000080bf0000000000000000bf06803b0000803bbf06803b0000803b0ad7a3bc33333bc09e009bbf000080bf0000000000000000f8037f3f0000803bf8037f3f0000803b0ad7a3bc33333b409e009b3f000080bf00000000000000000000803b7153d43e0000803b7153d43e0ad7a3bc33333bc09e009b3f000080bf0000000000000000eb037f3f7153d43eeb037f3f7153d43e0ad7a33c33333bc09e009bbf0000803f0000000000000000bf06803b7153d63ebf06803b7153d63e0ad7a33c33333b409e009bbf0000803f0000000000000000f8037f3f7153d63ef8037f3f7153d63e0ad7a33c33333bc09e009b3f0000803f00000000000000000000803b7153543f0000803b7153543f0ad7a33c33333b409e009b3f0000803f0000000000000000eb037f3f7153543feb037f3f7153543f0ad7a33c33333b409e009bbf000000000000803f000000000900803b59895c3f0900803b59895c3f0ad7a3bc33333b409e009bbf000000000000803f000000000000803bb7cc5a3f0000803bb7cc5a3f0ad7a33c33333b409e009b3f000000000000803f000000006d53d43e59895c3f6d53d43e59895c3f0ad7a3bc33333b409e009b3f000000000000803f000000006d53d43eb7cc5a3f6d53d43eb7cc5a3f0ad7a3bc33333bc09e009bbf00000000000080bf000000006d53d43e59895d3f6d53d43e59895d3f0ad7a33c33333bc09e009bbf00000000000080bf000000006d53d43efc455f3f6d53d43efc455f3f0ad7a3bc33333bc09e009b3f00000000000080bf000000000000803b59895d3f0000803b59895d3f0ad7a33c33333bc09e009b3f00000000000080bf000000000900803bfc455f3f0900803bfc455f3f
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.02, y: 2.925, z: 1.2109563}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
