%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Sinnerlig_Cable_Cover_Mesh
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.023311276, y: 0.41521934, z: 0.02338602}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200020001000300040005000600060005000700080009000a000a0009000b000c000d000e000e000d000f00100011001200120011001300140015001600160015001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 32
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 960
    _typelessdata: 4af7bebca197d4be0a94bf3c00000000000000000000803ff8037f400000803cf8037f3f0000803b4af7be3ca197d4be0a94bf3c00000000000000000000803ff8037f40dc2c743ef8037f3fdc2c743d4af7bebca197d43e0a94bf3c00000000000000000000803f0000803c0000803c0000803b0000803b4af7be3ca197d43e0a94bf3c00000000000000000000803f2600803cdb2c743e2600803bdb2c743d4af7be3ca197d4be0a94bfbc0000000000000000000080bf2b00803cdc2cf43e2b00803bdc2cf43d4af7bebca197d4be0a94bfbc0000000000000000000080bf0000803c6e16823e0000803b6e16823d4af7be3ca197d43e0a94bfbc0000000000000000000080bff8037f40dc2cf43ef8037f3fdc2cf43d4af7bebca197d43e0a94bfbc0000000000000000000080bff8037f406e16823ef8037f3f6e16823d4af7bebca197d43e0a94bfbc000080bf0000000000000000fd03803cdc2cfc3efd03803bdc2cfc3d4af7bebca197d4be0a94bfbc000080bf0000000000000000f8037f40dc2cfc3ef8037f3fdc2cfc3d4af7bebca197d43e0a94bf3c000080bf00000000000000000000803c7950373f0000803b7950373e4af7bebca197d4be0a94bf3c000080bf0000000000000000f0037f407950373ff0037f3f7950373e4af7be3ca197d4be0a94bfbc0000803f0000000000000000fd03803c79503b3ffd03803b79503b3e4af7be3ca197d43e0a94bfbc0000803f0000000000000000f8037f4079503b3ff8037f3f79503b3e4af7be3ca197d4be0a94bf3c0000803f00000000000000000000803c858a743f0000803b858a743e4af7be3ca197d43e0a94bf3c0000803f0000000000000000f0037f40858a743ff0037f3f858a743e4af7be3ca197d43e0a94bfbc00000000ffff7f3f000000000100803c7a50b73f0100803b7a50b73e4af7bebca197d43e0a94bfbc00000000ffff7f3f000000000000803cdeca9a3f0000803bdeca9a3e4af7be3ca197d43e0a94bf3c00000000ffff7f3f000000002ae8743e7a50b73f2ae8743d7a50b73e4af7bebca197d43e0a94bf3c00000000ffff7f3f000000002ae8743edeca9a3f2ae8743ddeca9a3e4af7bebca197d4be0a94bfbc00000000ffff7fbf000000002ae8743e858a783f2ae8743d858a783e4af7be3ca197d4be0a94bfbc00000000ffff7fbf000000002ae8743edeca983f2ae8743ddeca983e4af7bebca197d4be0a94bf3c00000000ffff7fbf000000000000803c858a783f0000803b858a783e4af7be3ca197d4be0a94bf3c00000000ffff7fbf000000000100803cdeca983f0100803bdeca983e
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.023311276, y: 0.41521934, z: 0.02338602}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
