%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Soft_Seat_Pillow2_LOD0_Cover_Mesh
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.28061727, y: 0.24746063, z: 0.16144134}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200020001000300040005000600060005000700080009000a000a0009000b000c000d000e000e000d000f00100011001200120011001300140015001600160015001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 32
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 960
    _typelessdata: 11ad8fbe52667dbee150253e00000000000000000000803f0000803b9947913e0000803b9947913e11ad8f3e52667dbee150253e00000000000000000000803f8092f73e9947913e8092f73e9947913e11ad8fbe52667d3ee150253e00000000000000000000803f2500803b00eb343f2500803b00eb343f11ad8f3e52667d3ee150253e00000000000000000000803f8092f73e00eb343f8092f73e00eb343f11ad8f3e52667dbee15025be0000000000000000000080bf8092f93e9947913e8092f93e9947913e11ad8fbe52667dbee15025be0000000000000000000080bf8092773f9947913e8092773f9947913e11ad8f3e52667d3ee15025be0000000000000000000080bf8192f93e00eb343f8192f93e00eb343f11ad8fbe52667d3ee15025be0000000000000000000080bf8092773f00eb343f8092773f00eb343f11ad8fbe52667d3ee15025be000080bf0000000000000000b700803b00eb353fb700803b00eb353f11ad8fbe52667dbee15025be000080bf00000000000000006d8eda3e00eb353f6d8eda3e00eb353f11ad8fbe52667d3ee150253e000080bf00000000000000000000803bcc8e7c3f0000803bcc8e7c3f11ad8fbe52667dbee150253e000080bf00000000000000006b8eda3ecc8e7c3f6b8eda3ecc8e7c3f11ad8f3e52667dbee15025be0000803f00000000000000008392f93e00eb353f8392f93e00eb353f11ad8f3e52667d3ee15025be0000803f00000000000000007710693f00eb353f7710693f00eb353f11ad8f3e52667dbee150253e0000803f00000000000000008092f93ecc8e7c3f8092f93ecc8e7c3f11ad8f3e52667d3ee150253e0000803f00000000000000007610693fcc8e7c3f7610693fcc8e7c3f11ad8f3e52667d3ee15025be000000000000803f000000007c02803b0000803b7c02803b0000803b11ad8fbe52667d3ee15025be000000000000803f000000008192f73e0000803b8192f73e0000803b11ad8f3e52667d3ee150253e000000000000803f000000000000803b99478f3e0000803b99478f3e11ad8fbe52667d3ee150253e000000000000803f000000007792f73e99478f3e7792f73e99478f3e11ad8fbe52667dbee15025be00000000000080bf000000008b92f93e0000803b8b92f93e0000803b11ad8f3e52667dbee15025be00000000000080bf000000008192773f0000803b8192773f0000803b11ad8fbe52667dbee150253e00000000000080bf000000008192f93e99478f3e8192f93e99478f3e11ad8f3e52667dbee150253e00000000000080bf000000007c92773f99478f3e7c92773f99478f3e
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.28061727, y: 0.24746063, z: 0.16144134}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
