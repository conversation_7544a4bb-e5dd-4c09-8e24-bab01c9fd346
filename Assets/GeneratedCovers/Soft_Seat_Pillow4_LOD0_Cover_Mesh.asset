%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Soft_Seat_Pillow4_LOD0_Cover_Mesh
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.26376775, y: 0.113616675, z: 0.28399518}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200020001000300040005000600060005000700080009000a000a0009000b000c000d000e000e000d000f00100011001200120011001300140015001600160015001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 32
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 960
    _typelessdata: 910c87bedcafe8bdd167913e00000000000000000000803ffa02803b1f9d233ffa02803b1f9d233f910c873edcafe8bdd167913e00000000000000000000803f8c84e23e1f9d233f8c84e23e1f9d233f910c87bedcafe83dd167913e00000000000000000000803f0000803bfef7533f0000803bfef7533f910c873edcafe83dd167913e00000000000000000000803f8084e23efef7533f8084e23efef7533f910c873edcafe8bdd16791be0000000000000000000080bf3ebcf53e1f9d233f3ebcf53e1f9d233f910c87bedcafe8bdd16791be0000000000000000000080bf5f206b3f1f9d233f5f206b3f1f9d233f910c873edcafe83dd16791be0000000000000000000080bf32bcf53efef7533f32bcf53efef7533f910c87bedcafe83dd16791be0000000000000000000080bf59206b3ffef7533f59206b3ffef7533f910c87bedcafe83dd16791be000080bf00000000000000008300803b716b453e8300803b716b453e910c87bedcafe8bdd16791be000080bf00000000000000000000803b0000803b0000803b0000803b910c87bedcafe83dd167913e000080bf00000000000000003abcf33e716b453e3abcf33e716b453e910c87bedcafe8bdd167913e000080bf000000000000000038bcf33e0000803b38bcf33e0000803b910c873edcafe8bdd16791be0000803f000000000000000039bc733f0000803b39bc733f0000803b910c873edcafe83dd16791be0000803f00000000000000003abc733f716b453e3abc733f716b453e910c873edcafe8bdd167913e0000803f00000000000000003abcf53e0000803b3abcf53e0000803b910c873edcafe83dd167913e0000803f00000000000000003cbcf53e716b453e3cbcf53e716b453e910c873edcafe83dd16791be00000000ffff7f3f0000000032bcf53e1f9d223f32bcf53e1f9d223f910c87bedcafe83dd16791be00000000ffff7f3f0000000032bcf53e716b493e32bcf53e716b493e910c873edcafe83dd167913e00000000ffff7f3f0000000031bc733f1f9d223f31bc733f1f9d223f910c87bedcafe83dd167913e00000000ffff7f3f0000000031bc733f726b493e31bc733f726b493e910c87bedcafe8bdd16791be00000000ffff7fbf0000000031bcf33e716b493e31bcf33e716b493e910c873edcafe8bdd16791be00000000ffff7fbf0000000032bcf33e1f9d223f32bcf33e1f9d223f910c87bedcafe8bdd167913e00000000ffff7fbf000000000000803b716b493e0000803b716b493e910c873edcafe8bdd167913e00000000ffff7fbf000000001900803b1f9d223f1900803b1f9d223f
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.26376775, y: 0.113616675, z: 0.28399518}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
