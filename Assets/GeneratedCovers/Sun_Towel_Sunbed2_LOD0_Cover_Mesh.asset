%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Sun_Towel_Sunbed2_LOD0_Cover_Mesh
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.84562445, y: 0.2683252, z: 0.4307342}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200020001000300040005000600060005000700080009000a000a0009000b000c000d000e000e000d000f00100011001200120011001300140015001600160015001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 32
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 960
    _typelessdata: d87a58bfec6189be3189dc3e00000000000000000000803f0204803c0000803c0204803b0000803bd87a583fec6189be3189dc3e00000000000000000000803f19c717400000803c19c7173f0000803bd87a58bfec61893e3189dc3e00000000000000000000803f0000803c8a5f433f0000803b8a5f433ed87a583fec61893e3189dc3e00000000000000000000803f11c717408a5f433f11c7173f8a5f433ed87a583fec6189be3189dcbe0000000000000000000080bf0204803c8a5f473f0204803b8a5f473ed87a58bfec6189be3189dcbe0000000000000000000080bf19c717408a5f473f19c7173f8a5f473ed87a583fec61893e3189dcbe0000000000000000000080bf0000803c8a5fc33f0000803b8a5fc33ed87a58bfec61893e3189dcbe0000000000000000000080bf11c717408a5fc33f11c7173f8a5fc33ed87a58bfec61893e3189dcbe000080bf00000000000000001bc71840875f433f1bc7183f875f433ed87a58bfec6189be3189dcbe000080bf000000000000000019c718400000803c19c7183f0000803bd87a58bfec61893e3189dc3e000080bf00000000000000003f946540875f433f3f94653f875f433ed87a58bfec6189be3189dc3e000080bf00000000000000003d9465400000803c3d94653f0000803bd87a583fec6189be3189dcbe0000803f00000000000000003d9465408a5f473f3d94653f8a5f473ed87a583fec61893e3189dcbe0000803f00000000000000003f946540895fc33f3f94653f895fc33ed87a583fec6189be3189dc3e0000803f000000000000000019c718408a5f473f19c7183f8a5f473ed87a583fec61893e3189dc3e0000803f00000000000000001bc71840895fc33f1bc7183f895fc33ed87a583fec61893e3189dcbe000000000000803f00000000d103803c8a5fc53fd103803b8a5fc53ed87a58bfec61893e3189dcbe000000000000803f0000000019c717408a5fc53f19c7173f8a5fc53ed87a583fec61893e3189dc3e000000000000803f000000000000803cec7c2f400000803bec7c2f3fd87a58bfec61893e3189dc3e000000000000803f0000000011c71740ec7c2f4011c7173fec7c2f3fd87a58bfec6189be3189dcbe00000000000080bf00000000d103803cec7c3040d103803bec7c303fd87a583fec6189be3189dcbe00000000000080bf0000000019c71740ec7c304019c7173fec7c303fd87a58bfec6189be3189dc3e00000000000080bf000000000000803c134a7d400000803b134a7d3fd87a583fec6189be3189dc3e00000000000080bf0000000011c71740134a7d4011c7173f134a7d3f
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.84562445, y: 0.2683252, z: 0.4307342}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
