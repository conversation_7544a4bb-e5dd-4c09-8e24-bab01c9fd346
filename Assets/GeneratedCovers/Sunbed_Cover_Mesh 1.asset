%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Sunbed_Cover_Mesh 1
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 1.1178775, y: 0.16601877, z: 0.4352031}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200020001000300040005000600060005000700080009000a000a0009000b000c000d000e000e000d000f00100011001200120011001300140015001600160015001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 32
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 960
    _typelessdata: 9c168fbfd3002abef1d2de3e00000000000000000000803ff104803c3d8a2040f104803b3d8a203f9c168f3fd3002abef1d2de3e00000000000000000000803fce544b403d8a2040ce544b3f3d8a203f9c168fbfd3002a3ef1d2de3e00000000000000000000803f0000803cb6963e400000803bb6963e3f9c168f3fd3002a3ef1d2de3e00000000000000000000803fc5544b40b6963e40c5544b3fb6963e3f9c168f3fd3002abef1d2debe0000000000000000000080bff104803cb6963f40f104803bb6963f3f9c168fbfd3002abef1d2debe0000000000000000000080bfce544b40b6963f40ce544b3fb6963f3f9c168f3fd3002a3ef1d2debe0000000000000000000080bf0000803c2fa35d400000803b2fa35d3f9c168fbfd3002a3ef1d2debe0000000000000000000080bfc5544b402fa35d40c5544b3f2fa35d3f9c168fbfd3002a3ef1d2debe000080bf00000000000000004e00803ca6af7c404e00803ba6af7c3f9c168fbfd3002abef1d2debe000080bf00000000000000000000803c2fa35e400000803b2fa35e3f9c168fbfd3002a3ef1d2de3e000080bf00000000000000003e8a9f3fa6af7c403e8a9f3ea6af7c3f9c168fbfd3002abef1d2de3e000080bf00000000000000003d8a9f3f2fa35e403d8a9f3e2fa35e3f9c168f3fd3002abef1d2debe0000803f00000000000000003d8a1f402fa35e403d8a1f3f2fa35e3f9c168f3fd3002a3ef1d2debe0000803f00000000000000003e8a1f40a6af7c403e8a1f3fa6af7c3f9c168f3fd3002abef1d2de3e0000803f00000000000000003e8aa13f2fa35e403e8aa13e2fa35e3f9c168f3fd3002a3ef1d2de3e0000803f00000000000000003f8aa13fa6af7c403f8aa13ea6af7c3f9c168f3fd3002a3ef1d2debe000000000000803f000000006205803c0000803c6205803b0000803b9c168fbfd3002a3ef1d2debe000000000000803f00000000cf544b400000803ccf544b3f0000803b9c168f3fd3002a3ef1d2de3e000000000000803f000000000000803c3d8a9f3f0000803b3d8a9f3e9c168fbfd3002a3ef1d2de3e000000000000803f00000000c4544b403d8a9f3fc4544b3f3d8a9f3e9c168fbfd3002abef1d2debe00000000000080bf000000006205803c3d8aa13f6205803b3d8aa13e9c168f3fd3002abef1d2debe00000000000080bf00000000cf544b403d8aa13fcf544b3f3d8aa13e9c168fbfd3002abef1d2de3e00000000000080bf000000000000803c3d8a1f400000803b3d8a1f3f9c168f3fd3002abef1d2de3e00000000000080bf00000000c4544b403d8a1f40c4544b3f3d8a1f3f
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 1.1178775, y: 0.16601877, z: 0.4352031}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
