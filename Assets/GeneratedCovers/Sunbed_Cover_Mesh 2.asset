%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Sunbed_Cover_Mesh 2
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 1.1241403, y: 0.16601886, z: 0.45746568}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200020001000300040005000600060005000700080009000a000a0009000b000c000d000e000e000d000f00100011001200120011001300140015001600160015001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 32
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 960
    _typelessdata: d4e38fbfd9002abef138ea3e00000000000000000000803fdc04803c0a2a2540dc04803b0a2a253fd4e38f3fd9002abef138ea3e00000000000000000000803fac3e48400a2a2540ac3e483f0a2a253fd4e38fbfd9002a3ef138ea3e00000000000000000000803f0000803cf89642400000803bf896423fd4e38f3fd9002a3ef138ea3e00000000000000000000803fa23e4840f8964240a23e483ff896423fd4e38f3fd9002abef138eabe0000000000000000000080bfdc04803cf8964340dc04803bf896433fd4e38fbfd9002abef138eabe0000000000000000000080bfac3e4840f8964340ac3e483ff896433fd4e38f3fd9002a3ef138eabe0000000000000000000080bf0000803ce60361400000803be603613fd4e38fbfd9002a3ef138eabe0000000000000000000080bfa23e4840e6036140a23e483fe603613fd4e38fbfd9002a3ef138eabe000080bf00000000000000004900803cd2707f404900803bd2707f3fd4e38fbfd9002abef138eabe000080bf00000000000000000000803ce60362400000803be603623fd4e38fbfd9002a3ef138ea3e000080bf00000000000000000b2aa43fd2707f400b2aa43ed2707f3fd4e38fbfd9002abef138ea3e000080bf00000000000000000a2aa43fe60362400a2aa43ee603623fd4e38f3fd9002abef138eabe0000803f00000000000000000a2a2440e60362400a2a243fe603623fd4e38f3fd9002a3ef138eabe0000803f00000000000000000b2a2440d2707f400b2a243fd2707f3fd4e38f3fd9002abef138ea3e0000803f00000000000000000b2aa63fe60362400b2aa63ee603623fd4e38f3fd9002a3ef138ea3e0000803f00000000000000000c2aa63fd2707f400c2aa63ed2707f3fd4e38f3fd9002a3ef138eabe000000000000803f000000004b05803c0000803c4b05803b0000803bd4e38fbfd9002a3ef138eabe000000000000803f00000000ac3e48400000803cac3e483f0000803bd4e38f3fd9002a3ef138ea3e000000000000803f000000000000803c0a2aa43f0000803b0a2aa43ed4e38fbfd9002a3ef138ea3e000000000000803f00000000a23e48400a2aa43fa23e483f0a2aa43ed4e38fbfd9002abef138eabe00000000000080bf000000004b05803c0a2aa63f4b05803b0a2aa63ed4e38f3fd9002abef138eabe00000000000080bf00000000ac3e48400a2aa63fac3e483f0a2aa63ed4e38fbfd9002abef138ea3e00000000000080bf000000000000803c0a2a24400000803b0a2a243fd4e38f3fd9002abef138ea3e00000000000080bf00000000a23e48400a2a2440a23e483f0a2a243f
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 1.1241403, y: 0.16601886, z: 0.45746568}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
