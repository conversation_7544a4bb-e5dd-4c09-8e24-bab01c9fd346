%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: SwimmingPool_Border_Cover_Mesh
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 10.8386545, y: 0.034080468, z: 16.585243}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200020001000300040005000600060005000700080009000a000a0009000b000c000d000e000e000d000f00100011001200120011001300140015001600160015001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 32
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 960
    _typelessdata: 216b2dc1f6970bbd94ae844100000000000000000000803f0000803c70fc7b400000803b70fc7b3f216b2d41f6970bbd94ae844100000000000000000000803f4036f83f70fc7b404036f83e70fc7b3f216b2dc1f6970b3d94ae844100000000000000000000803f2003803c885f7c402003803b885f7c3f216b2d41f6970b3d94ae844100000000000000000000803f4d36f83f885f7c404d36f83e885f7c3f216b2d41f6970bbd94ae84c10000000000000000000080bf0000803c885f7d400000803b885f7d3f216b2dc1f6970bbd94ae84c10000000000000000000080bf4036f83f885f7d404036f83e885f7d3f216b2d41f6970b3d94ae84c10000000000000000000080bf2003803ca1c27d402003803ba1c27d3f216b2dc1f6970b3d94ae84c10000000000000000000080bf4d36f83fa1c27d404d36f83ea1c27d3f216b2dc1f6970b3d94ae84c1000080bf00000000000000000000803c6efcfa3f0000803b6efcfa3e216b2dc1f6970bbd94ae84c1000080bf00000000000000000000803c3e36fa3f0000803b3e36fa3e216b2dc1f6970b3d94ae8441000080bf00000000000000003c603d406efcfa3f3c603d3f6efcfa3e216b2dc1f6970bbd94ae8441000080bf00000000000000003c603d403e36fa3f3c603d3f3e36fa3e216b2d41f6970bbd94ae84c10000803f00000000000000003c603d406efcfc3f3c603d3f6efcfc3e216b2d41f6970b3d94ae84c10000803f00000000000000003c603d409ec2fd3f3c603d3f9ec2fd3e216b2d41f6970bbd94ae84410000803f00000000000000000000803c6efcfc3f0000803b6efcfc3e216b2d41f6970b3d94ae84410000803f00000000000000000000803c9ec2fd3f0000803b9ec2fd3e216b2d41f6970b3d94ae84c1000000000000803f000000005a00803c6dfc7a405a00803b6dfc7a3f216b2dc1f6970b3d94ae84c1000000000000803f000000000000803c9ec2ff3f0000803b9ec2ff3e216b2d41f6970b3d94ae8441000000000000803f000000003b603d4070fc7a403b603d3f70fc7a3f216b2dc1f6970b3d94ae8441000000000000803f000000003a603d40a4c2ff3f3a603d3fa4c2ff3e216b2dc1f6970bbd94ae84c100000000000080bf000000003a603d400000803c3a603d3f0000803b216b2d41f6970bbd94ae84c100000000000080bf000000003d603d403c36f83f3d603d3f3c36f83e216b2dc1f6970bbd94ae844100000000000080bf000000000000803c4600803c0000803b4600803b216b2d41f6970bbd94ae844100000000000080bf000000007e01803c3e36f83f7e01803b3e36f83e
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 10.8386545, y: 0.034080468, z: 16.585243}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
