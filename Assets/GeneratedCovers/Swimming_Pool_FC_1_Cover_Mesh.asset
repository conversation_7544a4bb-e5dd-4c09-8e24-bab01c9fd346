%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Swimming_Pool_FC_1_Cover_Mesh
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 3.184894, y: 0.3247997, z: 9.706095}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200020001000300040005000600060005000700080009000a000a0009000b000c000d000e000e000d000f00100011001200120011001300140015001600160015001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 24
      format: 0
      dimension: 2
    - stream: 0
      offset: 32
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 960
    _typelessdata: 4ed54bc0254ca6be2a4c1b4100000000000000000000803fcc01803bd0b33c3fcc01803bd0b33c3f4ed54b40254ca6be2a4c1b4100000000000000000000803fb1b3a83ed0b33c3fb1b3a83ed0b33c3f4ed54bc0254ca63e2a4c1b4100000000000000000000803f0000803be033453f0000803be033453f4ed54b40254ca63e2a4c1b4100000000000000000000803faab3a83ee033453faab3a83ee033453f4ed54b40254ca6be2a4c1bc10000000000000000000080bfcc01803be033463fcc01803be033463f4ed54bc0254ca6be2a4c1bc10000000000000000000080bfb1b3a83ee033463fb1b3a83ee033463f4ed54b40254ca63e2a4c1bc10000000000000000000080bf0000803bf0b34e3f0000803bf0b34e3f4ed54bc0254ca63e2a4c1bc10000000000000000000080bfaab3a83ef0b34e3faab3a83ef0b34e3f4ed54bc0254ca63e2a4c1bc1000080bf00000000000000002100803bc133323f2100803bc133323f4ed54bc0254ca6be2a4c1bc1000080bf00000000000000000000803bb1b3293f0000803bb1b3293f4ed54bc0254ca63e2a4c1b41000080bf0000000000000000f2037f3fc133323ff2037f3fc133323f4ed54bc0254ca6be2a4c1b41000080bf0000000000000000f1037f3fb1b3293ff1037f3fb1b3293f4ed54b40254ca6be2a4c1bc10000803f0000000000000000f1037f3fc133333ff1037f3fc133333f4ed54b40254ca63e2a4c1bc10000803f0000000000000000f2037f3fd0b33b3ff2037f3fd0b33b3f4ed54b40254ca6be2a4c1b410000803f00000000000000000000803bc133333f0000803bc133333f4ed54b40254ca63e2a4c1b410000803f00000000000000002100803bd0b33b3f2100803bd0b33b3f4ed54b40254ca63e2a4c1bc1000000000000803f000000005001803bb0b3a83e5001803bb0b3a83e4ed54bc0254ca63e2a4c1bc1000000000000803f000000000000803bc901803b0000803bc901803b4ed54b40254ca63e2a4c1b41000000000000803f00000000f8037f3fa9b3a83ef8037f3fa9b3a83e4ed54bc0254ca63e2a4c1b41000000000000803f00000000f6037f3f0000803bf6037f3f0000803b4ed54bc0254ca6be2a4c1bc100000000000080bf00000000f6037f3fb9b3aa3ef6037f3fb9b3aa3e4ed54b40254ca6be2a4c1bc100000000000080bf00000000f6037f3fb1b3283ff6037f3fb1b3283f4ed54bc0254ca6be2a4c1b4100000000000080bf000000000000803bb0b3aa3e0000803bb0b3aa3e4ed54b40254ca6be2a4c1b4100000000000080bf000000000100803badb3283f0100803badb3283f
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 3.184894, y: 0.3247997, z: 9.706095}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
