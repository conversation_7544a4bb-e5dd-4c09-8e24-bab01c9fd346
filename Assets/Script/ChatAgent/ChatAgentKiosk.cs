using System.Collections;
using System.Text;
using Unity.WebRTC;
using UnityEngine;
using UnityEngine.Networking;
using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using UnityEngine.UI;
using MHXP.core;


#if UNITY_EDITOR
using UnityEditor;
#endif



public class ToggleImageVisibilityProperties : ToolProperties
{
    public ToolProperty visible = new ToolProperty { type = "boolean" };
}


public class ContentLabelProperties : ToolProperties
{
    public ToolProperty content_label = new ToolProperty { type = "string" };
    //  public ToolProperty bullet_key = new ToolProperty { type = "string" };
}

[RequireComponent(typeof(AudioSource))]
public class ChatAgentKiosk : ChatAgentAbstract
{
    private void Awake()
    {
        DontDestroyOnLoad(gameObject);
    }
    private UImangerProjectLevel uImangerProjectLevel;

    public void SetUIManagerProjectLevel(UImangerProjectLevel uImangerProjectLevel)
    {
        Debug.Log("Setting uImangerProjectLevel");
        this.uImangerProjectLevel = uImangerProjectLevel;
    }

    public void imageToggleCallback(string arguments, ActionFinishAgentTrigger finishCallback)
    {
        Dictionary<string, bool> dict = JsonConvert.DeserializeObject<Dictionary<string, bool>>(arguments);

        Debug.Log("Toggling image visibility " + arguments);
        if (dict.ContainsKey("visible"))
        {
            //  kioskUIManager.ToggleImageVisibility(!dict["visible"]); //not sure why negation is needed. jsut worked. // removing for now.
        }
        else
        {
            Debug.Log("No visible key in arguments");
        }

        // return " done!";
    }

    private void HighlightCurrentContent(string arguments, ActionFinishAgentTrigger finishTrigger)
    {
        Debug.Log("HighlightCurrentContent called ! Setting current topic and bullet " + arguments);
        Dictionary<string, string> dict = JsonConvert.DeserializeObject<Dictionary<string, string>>(arguments);
        if (dict.ContainsKey("content_label"))
        {
            uImangerProjectLevel.ShowContent(dict["content_label"], finishTrigger);
        }
        else
        {
            Debug.LogError("No topic_key or bullet_key in arguments");
        }

        //   finishTrigger.Invoke("Content Shown, please continue.");
    }


    private void HighLightAreaInVirtualTour(string arguments, ActionFinishAgentTrigger finishTrigger)
    {
        Debug.Log("HighLightAreaInVirtualTour called ! Setting current topic and bullet " + arguments);
        Dictionary<string, string> dict = JsonConvert.DeserializeObject<Dictionary<string, string>>(arguments);
        if (dict.ContainsKey("content_label"))
        {
            uImangerProjectLevel.HighlightAreaInVirtualTour(dict["content_label"], finishTrigger);
        }
        else
        {
            Debug.Log("No topic_key or bullet_key in arguments");
        }
        // "Content Shown in Virtual Tour, please continue.";
    }
    private ToolDefinition imageVisibilityToolDefinition = null;

    private ToolDefinition highlightContentToolDefinition = null;
    private ToolDefinition highlightAreaInVirtualTourToolDefinition = null;

    public void UpdateAgentContext_OnNewLocation(string location_label)
    {
        if (this.isSessionActive)
        {
            this.CancelInProgressResponse();
            this.SendSystemMsgToAgent("UserNavigated to : " + location_label);
        }
        else
        {
            Debug.Log("Session is not active, cannot update agent context");
        }
    }

    public ChatAgentKiosk()
    {
        imageVisibilityToolDefinition = new ToolDefinition
            (new ToolConfig
            {
                type = "function",
                name = "ToggleImageVisibility",
                description = "When the customer wants show (true) or hide (false) the image",
                parameters = new ToolParameters
                {
                    type = "object",
                    properties = new ToggleImageVisibilityProperties(),
                    required = new string[] { "visible" }
                }
            },
            imageToggleCallback);

        highlightContentToolDefinition = new ToolDefinition
            (new ToolConfig
            {
                type = "function",
                name = "HighlightCurrentContent",
                description = "Called before the agent starts talking about a specific content/topic. ",
                parameters = new ToolParameters
                {
                    type = "object",
                    properties = new ContentLabelProperties(),
                    required = new string[] { "content_label" }
                }
            }, HighlightCurrentContent
            );

        highlightAreaInVirtualTourToolDefinition = new ToolDefinition
          (new ToolConfig
          {
              type = "function",
              name = "HighLightAreaInVirtualTour",
              description = "Called before the agent starts talking about a specific part of the virtual tour. ",
              parameters = new ToolParameters
              {
                  type = "object",
                  properties = new ContentLabelProperties(),
                  required = new string[] { "content_label" }
              }
          }, HighLightAreaInVirtualTour
            );
    }


    protected override ToolDefinition[] GetCurrentAvailableToolDefinitions()
    {
        return new ToolDefinition[] { imageVisibilityToolDefinition, highlightContentToolDefinition, highlightAreaInVirtualTourToolDefinition };
    }


    protected override string GetCurrentPromptInstruction()
    {
        var new_instruction = instruction_template
                  .Replace("__name__", (customer.customerName != null && customer.customerName != "") ? customer.customerName : "Still Uknown")
                  .Replace("__location__", customer.currentLocation);

        if (!string.IsNullOrEmpty(houseInformation))
        {
            new_instruction = instruction_template.Replace("__HouseInformation__", houseInformation);
        }

        DebugLog("Sending fresh context..." + new_instruction);  // Add logging 
        return new_instruction;

    }

}

