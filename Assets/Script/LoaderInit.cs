using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using static Meta.XR.MRUtilityKit.Data;

namespace MHXP.core.SceneManagement
{
    public class LoaderInit : MonoBehaviour
    {
        [SerializeField] SceneList sceneList;
        [SerializeField] Transform cameraRig; // Add camera rig field
        SceneData nextScene;

        public event System.Action<SceneData> OnNextSceneSet;

        private void Start()
        {
            if (sceneList == null)
            {
                sceneList = Resources.Load<SceneList>("SceneData");
            }
            if (sceneList == null)
            {
                Debug.Log("Error!!! Can not find scene data");
            }
        }

        public void SetSceneIndex(string sceneName)
        {
            nextScene = sceneList.scenes.Find(X => X.sceneName == sceneName);
            OnNextSceneSet?.Invoke(nextScene);
        }

       

        public SceneData GetNextScene() { return nextScene; }

        public void SetLoaderView()
        {
            if (nextScene == null)
                return;

            // Move camera rig to transition position if assigned
            if (cameraRig != null)
            {
                cameraRig.position = nextScene.transitionCameraPosition;
                cameraRig.rotation = Quaternion.Euler(nextScene.transitionCameraRotation);
                Debug.Log($"Camera rig moved to transition position ({nextScene.transitionCameraPosition}) and rotation ({nextScene.transitionCameraRotation}) for scene: {nextScene.sceneName}");
            }
            else
            {
                Debug.LogWarning("Camera rig not assigned in LoaderInit!");
            }

            if (nextScene.loaderView != null)
            {

                // RenderSettings.skybox = nextScene.loaderView;

                Instantiate(nextScene.loaderView, Vector3.zero,Quaternion.identity);
            }
            else
            {
                Debug.LogWarning($"Transition prefab not found in SceneData for scene");
            }

        }
    }
}