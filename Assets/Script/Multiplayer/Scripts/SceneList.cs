using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace MHXP.core.SceneManagement
{
    [CreateAssetMenu(fileName = "SceneData", menuName = "Data/Create Scene Data", order = 1)]
    public class SceneList : ScriptableObject
    {
        public List<SceneData> scenes;

        public string GetNextScene (string sceneName)
        {
            string returnName = "";
            SceneData sceneData = null;

            sceneData = scenes.Find(X => X.sceneName == sceneName);

            if (sceneData == null)
            {
                returnName = scenes[0].sceneName;
            }
            else
            {
                int sceneIndex = scenes.IndexOf(sceneData);
                sceneIndex++;

                if (sceneIndex >= scenes.Count)
                {
                    returnName = "IntroCalibrationScene";
                }
                else
                {
                    returnName = scenes[sceneIndex].sceneName;
                }
            }

            return returnName;
        }
    }

    [System.Serializable]
    public class SceneData
    {
        public string buttonName;
        public string sceneName;
        public GameObject loaderView;
        public Vector3 transitionCameraPosition;
        public Vector3 transitionCameraRotation;
    }
}