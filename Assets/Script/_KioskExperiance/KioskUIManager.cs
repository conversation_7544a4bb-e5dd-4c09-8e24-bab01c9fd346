using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using static KioskControlPanelManager;

public class KioskUIManager : MonoBehaviour
{
    private static KioskUIManager instance;
    private KioskControlPanelManager kioskControlPanel;

    [SerializeField] public ChatAgentKiosk chatAgent;

    List<ContentNode> contentNodes;

    private int currentIndex = 0;
    private KioskContentAdapter kioskContentAdapter;

    [Header("Prefabs")]
    public GameObject hierarchyButtonPrefab;

    [Header("Menu Icons")]
    public Sprite hamburgerIcon;
    public Sprite closeIcon;


    void Awake()
    {
        if (instance == null)
            instance = this;
        else
            Destroy(gameObject);

        kioskControlPanel = gameObject.AddComponent<KioskControlPanelManager>();
        DontDestroyOnLoad(gameObject); // Ensure this object persists across scenes
    }
    void Start()
    {

        kioskContentAdapter = GetComponent<KioskContentAdapter>();
        if (kioskContentAdapter == null)
        {
            Debug.LogError("KioskContentAdapter not found in the hierarchy. Please ensure it exists. Add a sub class of KioskContentAdapter");
            return;
        }
       // chatAgent.SetKioskUIManager(this); disabled moved to UImanagerProjectLevel.cs
        Debug.Log("Spawning buttons");
        prepareContentNode();
        kioskControlPanel.SpawnButtons(contentNodes);
        Debug.Log("Buttons spawned");
    }

    public void StartAgent()
    {
        chatAgent.StartSession();
    }
    public void StopAgent()
    {
        chatAgent.StopSession();
    }



    private void prepareContentNode()
    {
        contentNodes = kioskContentAdapter.PrepareContentNodeTree();
    }



    public void ShowContent(string content_label, ActionFinishAgentTrigger finishCallback)
    {
        if (kioskContentAdapter.GetContentNode(content_label) != null)
        {
            kioskControlPanel.ShowContentNode(kioskContentAdapter.GetContentNode(content_label), finishCallback);
        }
        else
        {
            finishCallback.Invoke("Content [" + content_label + "] Not Found, please explain verbally.");
        }
    }

   

    internal void HighlightAreaInVirtualTour(string content_label, ActionFinishAgentTrigger finishCallback)
    {
        ShowContent(content_label, finishCallback);
        // twoBhkVirtualHomeManager.GoToLocation(content_label); //TODO - split the content_lable - and get the right virtual home manager and location. - for now only one.
    }
}
