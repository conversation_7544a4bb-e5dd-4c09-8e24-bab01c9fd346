using UnityEngine;
using UnityEngine.SceneManagement;
using System;
using System.Collections;

public class AppController : MonoBehaviour
{
    UImangerProjectLevel uiManagerProjectLevel;
    UImangerProjectLevel UImangerProjectLevel => uiManagerProjectLevel ??= FindFirstObjectByType<UImangerProjectLevel>();

    TouchOrbitCamera touchOrbitCamera;
    TouchOrbitCamera TouchOrbitCamera => touchOrbitCamera ??= FindFirstObjectByType<TouchOrbitCamera>();

    MHXP.TabletMode.DataController dataController;
    public MHXP.TabletMode.DataController DataController => dataController ??= GetComponent<MHXP.TabletMode.DataController>();

    HotspotsManager hotspotsManager;
    HotspotsManager HotspotsManager => hotspotsManager ??= FindFirstObjectByType<HotspotsManager>();

    public string sceneToGoTo { get; set; } = "";

    //public void GoToUnit(int sceneNumber)
    //{
    //    SceneManager.LoadScene(sceneNumber);
    //}

    //public void GoToUnit(string sceneName)
    //{
    //    SceneManager.LoadScene(sceneName);
    //}

    //public void EnableOrbitCamera(bool enable)
    //{
    //    TouchOrbitCamera.updateCameraPosition = enable;
    //}

    public void StartProjectOverview ()
    {
        UImangerProjectLevel.ShowProjectOverview();
        TouchOrbitCamera.StartAutoRotation();
    }

    public void OnClickTower(string towerName)
    {
        Transform hotspot = HotspotsManager.GetHotspotByName(towerName);
        if (hotspot == null)
        {
            Debug.LogError($"Hotspot with name {towerName} not found.");
            return;
        }
        Transform orbitCameraTarget = hotspot.gameObject.GetComponent<HotspotDataHolder>()?.touchOrbitCameraTarget;
        TouchOrbitCamera.MoveTargetTo(orbitCameraTarget);

        UImangerProjectLevel.ShowHotspotData(hotspot);
    }

    public void ShowUnit ()
    {
        if (!string.IsNullOrEmpty(sceneToGoTo))
        {
            if (SceneManager.GetActiveScene().name != sceneToGoTo)
            {
                SceneLoader.Instance.LoadSceneAsyncWithRenderedCallback(sceneToGoTo, () =>
                {
                    Debug.Log($"Scene {sceneToGoTo} loaded successfully.");
                    sceneToGoTo = ""; 
                });
            }
        }
    }

    public void ResetOrbitCamera ()
    {
        TouchOrbitCamera.ResetCameraToStart();
    }

    public void StartLocationContext ()
    {
        TouchOrbitCamera.ZoomTo(true);
    }

}
