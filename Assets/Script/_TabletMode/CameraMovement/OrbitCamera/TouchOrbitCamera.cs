﻿using UnityEngine;
using System.Collections;

public class TouchOrbitCamera : MonoBehaviour
{
    [Header("Orbit Settings")]
    public Transform target;
    public float rotationRadius = 5f;
    public float rotationSpeed = 0.2f;
    public float tiltSpeed = 0.2f;
    public float zoomSpeed = 0.1f;
    public float verticalOffset = 0f;

    public float minRadius = 2f;
    public float maxRadius = 20f;
    public float minXAngle = -80f;
    public float maxXAngle = 80f;

    [Header("Orbit Toggle")]
    public bool updateCameraPosition = true;

    [Header("Pan Settings")]
    public float panSpeed = 0.01f;
    public Vector2 panLimitX = new Vector2(-10f, 10f);
    public Vector2 panLimitZ = new Vector2(-10f, 10f);

    [Header("Transition Settings")]
    public float moveDuration = 1.5f;
    public AnimationCurve moveCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    private float currentYAngle = 0f;
    private float currentXAngle = 20f;
    private Vector2 lastInputPosition;
    private bool isDragging = false;
    private bool isPanning = false;

    private Vector3 savedViewPosition;
    private Quaternion savedViewRotation;
    private Vector3 savedViewScale;

    public static float NormalizedDistance { get; private set; }

    UImangerProjectLevel uiManagerProjectLevel;
    UImangerProjectLevel UImangerProjectLevel => uiManagerProjectLevel ??= FindFirstObjectByType<UImangerProjectLevel>(FindObjectsInactive.Include);

    private Vector3 initialCameraPosition;
    private Quaternion initialCameraRotation;
    private float initialRotationRadius;
    private float initialXAngle;
    private float initialYAngle;

    private Vector3 initialTargetPosition;


    void Start()
    {
        currentXAngle = transform.eulerAngles.x;
        currentYAngle = transform.eulerAngles.y;

        initialCameraPosition = transform.position;
        initialCameraRotation = transform.rotation;
        initialRotationRadius = rotationRadius;
        initialXAngle = currentXAngle;
        initialYAngle = currentYAngle;

        if (target != null)
            initialTargetPosition = target.position;
    }

    void Update()
    {
        if (updateCameraPosition)
        {
            if (!isAutoRotating)
            {
#if UNITY_EDITOR || UNITY_STANDALONE
                HandleMouseInput();
#else
        HandleTouchInput();
#endif
            }

            UpdateCameraPosition();
        }

        NormalizedDistance = Mathf.InverseLerp(minRadius, maxRadius, rotationRadius);
    }

    void HandleMouseInput()
    {
        bool leftClick = Input.GetMouseButton(0);
        bool rightClick = Input.GetMouseButton(1);

        if (leftClick || rightClick)
        {
            if (Input.GetMouseButtonDown(0) || Input.GetMouseButtonDown(1))
            {
                lastInputPosition = Input.mousePosition;
            }

            Vector2 delta = (Vector2)Input.mousePosition - lastInputPosition;
            lastInputPosition = Input.mousePosition;

            if (leftClick && rightClick)
            {
                // Two-button pan — restrict to XZ
                isPanning = true;

                //Vector3 forward = Vector3.ProjectOnPlane(transform.forward, Vector3.up).normalized;
                //Vector3 right = Vector3.Cross(Vector3.up, forward);
                //Vector3 move = (-right * delta.x + -forward * delta.y) * panSpeed;

                //target.position += move;
                //ClampTargetPosition();
            }
            else
            {
                // Orbit
                isPanning = false;
                currentYAngle += delta.x * rotationSpeed;
                currentXAngle -= delta.y * tiltSpeed;
                currentXAngle = Mathf.Clamp(currentXAngle, minXAngle, maxXAngle);
            }
        }

        if (Input.GetMouseButtonUp(0) || Input.GetMouseButtonUp(1))
        {
            isPanning = false;
        }

        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (Mathf.Abs(scroll) > 0.001f)
        {
            rotationRadius -= scroll * zoomSpeed * 100f;
            rotationRadius = Mathf.Clamp(rotationRadius, minRadius, maxRadius);
        }
    }


    void HandleTouchInput()
    {
        if (Input.touchCount == 1)
        {
            Touch touch = Input.GetTouch(0);

            switch (touch.phase)
            {
                case TouchPhase.Began:
                    lastInputPosition = touch.position;
                    isDragging = true;
                    break;

                case TouchPhase.Moved:
                    if (isDragging)
                    {
                        Vector2 delta = touch.position - lastInputPosition;
                        lastInputPosition = touch.position;

                        currentYAngle += delta.x * rotationSpeed;
                        currentXAngle -= delta.y * tiltSpeed;
                        currentXAngle = Mathf.Clamp(currentXAngle, minXAngle, maxXAngle);
                    }
                    break;

                case TouchPhase.Ended:
                case TouchPhase.Canceled:
                    isDragging = false;
                    break;
            }
        }
        else if (Input.touchCount == 2)
        {
            isDragging = false;

            Touch t0 = Input.GetTouch(0);
            Touch t1 = Input.GetTouch(1);

            Vector2 t0Prev = t0.position - t0.deltaPosition;
            Vector2 t1Prev = t1.position - t1.deltaPosition;

            float prevDistance = Vector2.Distance(t0Prev, t1Prev);
            float currDistance = Vector2.Distance(t0.position, t1.position);
            float deltaZoom = currDistance - prevDistance;

            rotationRadius -= deltaZoom * zoomSpeed;
            rotationRadius = Mathf.Clamp(rotationRadius, minRadius, maxRadius);

            // Two-finger pan — restrict to horizontal plane
            Vector2 avgDelta = (t0.deltaPosition + t1.deltaPosition) * 0.5f;
            Vector3 forward = Vector3.ProjectOnPlane(transform.forward, Vector3.up).normalized;
            Vector3 right = Vector3.Cross(Vector3.up, forward);
            Vector3 move = (-right * avgDelta.x + -forward * avgDelta.y) * panSpeed * 0.5f;

            target.position += move;
            ClampTargetPosition();
        }
    }

    void ClampTargetPosition()
    {
        Vector3 pos = target.position;
        pos.x = Mathf.Clamp(pos.x, panLimitX.x, panLimitX.y);
        pos.z = Mathf.Clamp(pos.z, panLimitZ.x, panLimitZ.y);
        pos.y = target.position.y; // keep height constant
        target.position = pos;
    }


    void UpdateCameraPosition()
    {
        if (target == null) return;

        float yaw = currentYAngle * Mathf.Deg2Rad;
        float pitch = Mathf.Clamp(currentXAngle, minXAngle, maxXAngle) * Mathf.Deg2Rad;

        Vector3 offset = new Vector3(
            Mathf.Sin(yaw) * Mathf.Cos(pitch),
            Mathf.Sin(pitch),
            Mathf.Cos(yaw) * Mathf.Cos(pitch)
        ) * rotationRadius;

        transform.position = target.position + offset + Vector3.up * verticalOffset;
        transform.LookAt(target.position + Vector3.up * verticalOffset);
    }

    private void ManualUpdateCameraPosition()
    {
        if (target == null) return;

        float yaw = currentYAngle * Mathf.Deg2Rad;
        float pitch = Mathf.Clamp(currentXAngle, minXAngle, maxXAngle) * Mathf.Deg2Rad;

        Vector3 offset = new Vector3(
            Mathf.Sin(yaw) * Mathf.Cos(pitch),
            Mathf.Sin(pitch),
            Mathf.Cos(yaw) * Mathf.Cos(pitch)
        ) * rotationRadius;

        transform.position = target.position + offset + Vector3.up * verticalOffset;
        transform.LookAt(target.position + Vector3.up * verticalOffset);
    }

    public void OrbitToTargetView(Transform view)
    {
        StopAllCoroutines();
        SyncOrbitStateFromTransform();

        Vector3 viewDir = view.position - (target.position + Vector3.up * verticalOffset);
        float finalRadius = viewDir.magnitude;

        float finalYaw = Mathf.Atan2(viewDir.x, viewDir.z) * Mathf.Rad2Deg;
        float forwardPitch = Mathf.Asin(view.forward.y / view.forward.magnitude) * Mathf.Rad2Deg;

        StartCoroutine(OrbitAroundTargetAndFinish(view, currentYAngle, finalYaw, currentXAngle, -forwardPitch, rotationRadius, finalRadius));
        UImangerProjectLevel.gameObject.SetActive(true);
        //UImangerProjectLevel.ShowHotspotData(view);
    }

    private IEnumerator OrbitAroundTargetAndFinish(
        Transform view,
        float fromY, float toY,
        float fromX, float toX,
        float fromRadius, float toRadius)
    {
        updateCameraPosition = false;

        ReturnToOrbitOverTime(false);
        if (hasOrbitReturnedOnce)
        {
            yield return new WaitForSeconds(0.75f);
        }
        hasOrbitReturnedOnce = true;

        isDragging = false;

        float elapsed = 0f;

        while (elapsed < moveDuration)
        {
            float t = elapsed / moveDuration;
            float curvedT = moveCurve.Evaluate(t);

            currentYAngle = Mathf.LerpAngle(fromY, toY, curvedT);
            currentXAngle = Mathf.Lerp(fromX, toX, curvedT);
            rotationRadius = Mathf.Lerp(fromRadius, toRadius, curvedT);

            ManualUpdateCameraPosition();

            elapsed += Time.deltaTime;
            yield return null;
        }

        currentYAngle = toY;
        currentXAngle = toX;
        rotationRadius = toRadius;
        ManualUpdateCameraPosition();

        savedViewPosition = view.position;
        savedViewRotation = view.rotation;
        savedViewScale = view.localScale;

        StartCoroutine(MatchViewTransformOverTime());
    }

    private IEnumerator MatchViewTransformOverTime()
    {
        Vector3 startPos = transform.position;
        Quaternion startRot = transform.rotation;
        Vector3 startScale = transform.localScale;

        float duration = 0.5f;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            float t = elapsed / duration;
            float curvedT = moveCurve.Evaluate(t);

            transform.position = Vector3.Lerp(startPos, savedViewPosition, curvedT);
            transform.rotation = Quaternion.Slerp(startRot, savedViewRotation, curvedT);
            transform.localScale = Vector3.Lerp(startScale, savedViewScale, curvedT);

            elapsed += Time.deltaTime;
            yield return null;
        }

        transform.position = savedViewPosition;
        transform.rotation = savedViewRotation;
        transform.localScale = savedViewScale;
    }

    private void SyncOrbitStateFromTransform()
    {
        if (target == null) return;

        Vector3 direction = transform.position - (target.position + Vector3.up * verticalOffset);
        rotationRadius = direction.magnitude;

        float yaw = Mathf.Atan2(direction.x, direction.z);
        float pitch = Mathf.Asin(direction.y / rotationRadius);

        currentYAngle = yaw * Mathf.Rad2Deg;
        currentXAngle = pitch * Mathf.Rad2Deg;
    }

    private void GetTargetOrbitPose(out Vector3 position, out Quaternion rotation)
    {
        float yaw = currentYAngle * Mathf.Deg2Rad;
        float pitch = Mathf.Clamp(currentXAngle, minXAngle, maxXAngle) * Mathf.Deg2Rad;

        Vector3 offset = new Vector3(
            Mathf.Sin(yaw) * Mathf.Cos(pitch),
            Mathf.Sin(pitch),
            Mathf.Cos(yaw) * Mathf.Cos(pitch)
        ) * rotationRadius;

        position = target.position + offset + Vector3.up * verticalOffset;
        rotation = Quaternion.LookRotation((target.position + Vector3.up * verticalOffset) - position);
    }

    public void ReturnToOrbitOverTime(bool shouldChangeUpdate = true)
    {
        StartCoroutine(SmoothReturnToOrbit(shouldChangeUpdate));
    }

    private IEnumerator SmoothReturnToOrbit(bool shouldChangeUpdate = true)
    {
        GetTargetOrbitPose(out Vector3 orbitPos, out Quaternion orbitRot);

        Vector3 startPos = transform.position;
        Quaternion startRot = transform.rotation;

        float duration = 0.75f;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            float t = elapsed / duration;
            float curvedT = moveCurve.Evaluate(t);

            transform.position = Vector3.Lerp(startPos, orbitPos, curvedT);
            transform.rotation = Quaternion.Slerp(startRot, orbitRot, curvedT);

            elapsed += Time.deltaTime;
            yield return null;
        }

        transform.position = orbitPos;
        transform.rotation = orbitRot;

        if (shouldChangeUpdate)
        {
            updateCameraPosition = true;
            hasOrbitReturnedOnce = false;
        }
    }

    private bool hasOrbitReturnedOnce = false;

    public void ResetOrbitReturnWait()
    {
        hasOrbitReturnedOnce = false;
    }

    #region AutoRotate

    private Coroutine autoRotateRoutine;
    private bool isAutoRotating = false;

    public void StartAutoRotation(float autoRotateSpeed = 10f)
    {
        if (isAutoRotating) return;

        isAutoRotating = true;
        updateCameraPosition = true; // Ensure Update() runs
        autoRotateRoutine = StartCoroutine(AutoRotateCoroutine(autoRotateSpeed));
    }

    public void StopAutoRotation()
    {
        if (!isAutoRotating) return;

        isAutoRotating = false;
        if (autoRotateRoutine != null)
            StopCoroutine(autoRotateRoutine);

        autoRotateRoutine = null;
    }

    private IEnumerator AutoRotateCoroutine(float speed)
    {
        while (isAutoRotating)
        {
            currentYAngle += speed * Time.deltaTime;
            UpdateCameraPosition();
            yield return null;
        }
    }
    
    #endregion AutoRotate

    #region ZoomInOut

    /// <summary>
    /// Smoothly zooms the camera to the specified target rotation radius over the given duration.
    /// </summary>
    /// <param name="targetRadius">The target rotation radius to zoom to.</param>
    /// <param name="duration">Time in seconds to reach the target zoom.</param>
    public void ZoomTo(bool zoomOut, float duration = 1.0f)
    {
        
        float targetRadius = zoomOut ? maxRadius : 1.4f;
        StartCoroutine(ZoomToCoroutine(targetRadius, duration));
    }

    private IEnumerator ZoomToCoroutine(float targetRadius, float duration)
    {
        float startRadius = rotationRadius;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            float t = elapsed / duration;
            float curvedT = moveCurve != null ? moveCurve.Evaluate(t) : t;
            rotationRadius = Mathf.Lerp(startRadius, targetRadius, curvedT);
            UpdateCameraPosition();
            elapsed += Time.deltaTime;
            yield return null;
        }

        rotationRadius = targetRadius;
        UpdateCameraPosition();
    }

    #endregion ZoomInOut 

    #region MoveTarget

    /// <summary>
    /// Smoothly moves the target to the specified transform's position over time.
    /// </summary>
    /// <param name="destination">The transform to move the target to.</param>
    /// <param name="duration">Time it takes to move the target.</param>
    public void MoveTargetTo(Transform destination, float duration = 1f)
    {
        if (target == null || destination == null) return;
        if (target.Equals(destination)) return;

        StopCoroutine(nameof(MoveTargetCoroutine));
        StartCoroutine(MoveTargetCoroutine(destination.position, duration));

        //UImangerProjectLevel.ShowHotspotData(destination);
    }

    private IEnumerator MoveTargetCoroutine(Vector3 destinationPosition, float duration)
    {
        Vector3 startPosition = target.position;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            float t = elapsed / duration;
            float curvedT = moveCurve != null ? moveCurve.Evaluate(t) : t;

            target.position = Vector3.Lerp(startPosition, destinationPosition, curvedT);
            elapsed += Time.deltaTime;
            yield return null;
        }

        target.position = destinationPosition;
    }
    #endregion MoveTarget


    #region ResetCamera 

    public void ResetCameraToStart(float duration = 1f)
    {
        StopCoroutine(nameof(ResetCameraOrbitCoroutine));
        StartCoroutine(ResetCameraOrbitCoroutine(duration));
    }


    private IEnumerator ResetCameraOrbitCoroutine(float duration)
    {
        float startY = currentYAngle;
        float startX = currentXAngle;
        float startRadius = rotationRadius;

        float endY = initialYAngle;
        float endX = initialXAngle;
        float endRadius = initialRotationRadius;

        Vector3 startTargetPos = target.position;
        Vector3 endTargetPos = initialTargetPosition;

        float elapsed = 0f;

        updateCameraPosition = false;

        while (elapsed < duration)
        {
            float t = elapsed / duration;
            float curvedT = moveCurve != null ? moveCurve.Evaluate(t) : t;

            currentYAngle = Mathf.LerpAngle(startY, endY, curvedT);
            currentXAngle = Mathf.Lerp(startX, endX, curvedT);
            rotationRadius = Mathf.Lerp(startRadius, endRadius, curvedT);

            // Smoothly move the target if it has changed
            target.position = Vector3.Lerp(startTargetPos, endTargetPos, curvedT);

            ManualUpdateCameraPosition();

            elapsed += Time.deltaTime;
            yield return null;
        }

        currentYAngle = endY;
        currentXAngle = endX;
        rotationRadius = endRadius;
        target.position = endTargetPos;

        ManualUpdateCameraPosition();

        updateCameraPosition = true;
    }

    #endregion ResetCamera
}
