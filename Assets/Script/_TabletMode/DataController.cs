using System.Collections.Generic;
using UnityEngine;
using System.Linq;

namespace MHXP.TabletMode
{
    [System.Serializable]
    public class UnitTypeData
    {
        public string UnitName;
        public string bedroomCount;
        public string unitArea;
        public string sceneName;

        public List<Sprite> sprites;
    }

    [System.Serializable]
    public class AmanitiesTypeData
    {
        public string amenityName;
        public List<Sprite> aminityImages;
        public string sceneName;
    }

    public class DataController : MonoBehaviour
    {
        public List<string> TowerNames { get; private set; } = new List<string>();
        [SerializeField] HotspotsManager hotspotsManager;

        public Transform originalOribitTargetTransform;

        [SerializeField] List<UnitTypeData> allUnits = new List<UnitTypeData>();
        public Dictionary<string, List<UnitTypeData>> groupedUnits { get; private set; } = new Dictionary<string, List<UnitTypeData>>();

        public List<AmanitiesTypeData> allAmanities = new List<AmanitiesTypeData>();

        private void Start()
        {
            GenarateHotspotNames();
            GroupUnitsByBedroomCount();
        }

        void GenarateHotspotNames ()
        {
            if (hotspotsManager == null)
                hotspotsManager = FindFirstObjectByType<HotspotsManager>();
            
            if (hotspotsManager == null)
            {
                Debug.LogError("HotspotsManager not found in the scene.");
                return;
            }

            TowerNames.Clear();
            List<Transform> allHotspots = hotspotsManager.GetAllHotSpots();

            foreach (Transform hotspot in allHotspots)
            {
                if (hotspot != null && !string.IsNullOrEmpty(hotspot.name))
                {
                    if (hotspot.gameObject.name.Contains("Tower"))
                        TowerNames.Add(hotspot.name);
                }
            }
        }


        /// <summary>
        /// Call this manually to group the units by normalized bedroom count.
        /// </summary>
        public void GroupUnitsByBedroomCount()
        {
            groupedUnits = allUnits
                .GroupBy(unit => NormalizeBedroomCount(unit.bedroomCount))
                .ToDictionary(group => group.Key, group => group.ToList());

            //// Optional: Debug print
            //foreach (var kvp in groupedUnits)
            //{
            //    Debug.Log($"Grouped: {kvp.Key} - {kvp.Value.Count} units");
            //}
        }

        /// <summary>
        /// Normalizes the bedroom count string (e.g., "2BHK" -> "2bhk", trims whitespace).
        /// </summary>
        private string NormalizeBedroomCount(string raw)
        {
            return string.IsNullOrWhiteSpace(raw) ? "" : raw.Trim().ToLowerInvariant();
        }
    }
}