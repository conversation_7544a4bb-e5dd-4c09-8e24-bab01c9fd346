using UnityEngine;
using CesiumForUnity;
using Unity.Mathematics;
using System.Collections.Generic;

[System.Serializable]
public class POI
{
    public string name;
    public double latitude;        
    public double longitude;
    public double height;
}

[System.Serializable]
public class POICategory
{
    public string categoryName;
    public POI[] pois;
}

public class CesiumPOIManager : MonoBehaviour
{
    [Header("References")]
    public GameObject poiPrefab;
    public CesiumGeoreference cesiumGeoreference; // Reference this in Inspector

    [Header("POI Data")]
    public POICategory[] poiCategories;

    [Header("Debug")]
    public int activeCategoryIndex = 0;

    private List<GameObject> spawnedPOIs = new List<GameObject>();

    //void Start()
    //{
    //    ShowCategory(activeCategoryIndex);
    //}

    public void ShowCategory (string categoryName)
    {
        int index = -1;
        foreach (var category in poiCategories)
        {
            if (category.categoryName == categoryName)
            {
                index = System.Array.IndexOf(poiCategories, category);
                break;
            }
        }

        if (index > -1)
            ShowCategory(index);
    }

    public void ShowCategory(int index)
    {
        ClearPOIs();

        if (index < 0 || index >= poiCategories.Length)
        {
            Debug.LogWarning("Invalid POI category index.");
            return;
        }

        POICategory category = poiCategories[index];
        activeCategoryIndex = index;

        InstantiatePOI(category.pois);
    }

    public void ShowAllPoi()
    {
        ClearPOIs();
        List<POI> allPois = new List<POI>();
        foreach (var category in poiCategories)
        {
            allPois.AddRange(category.pois);
        }

        InstantiatePOI(allPois.ToArray());
    }

    private void InstantiatePOI(POI[] pois)
    {
        foreach (POI poi in pois)
        {
            GameObject instance = Instantiate(poiPrefab, Vector3.zero, quaternion.identity);
            instance.transform.parent = cesiumGeoreference.gameObject.transform;
            instance.name = poi.name;

            CesiumGlobeAnchor anchor = instance.GetComponent<CesiumGlobeAnchor>();
            if (anchor == null)
                anchor = instance.AddComponent<CesiumGlobeAnchor>();

            anchor.longitudeLatitudeHeight = new double3(poi.longitude, poi.latitude, poi.height);

            if (cesiumGeoreference != null)
                anchor.longitudeLatitudeHeight = new double3(poi.longitude, poi.latitude, poi.height);
            //anchor.SetPositionLongitudeLatitudeHeight(poi.longitude, poi.latitude, poi.height);


            // Optional label
            var tmp = instance.GetComponentInChildren<TMPro.TextMeshPro>();
            if (tmp != null)
                tmp.text = poi.name;

            spawnedPOIs.Add(instance);
        }
    }

    public void NextCategory()
    {
        int next = (activeCategoryIndex + 1) % poiCategories.Length;
        ShowCategory(next);
    }

    private void ClearPOIs()
    {
        foreach (GameObject poi in spawnedPOIs)
        {
            if (poi != null)
                Destroy(poi);
        }
        spawnedPOIs.Clear();
    }
}
