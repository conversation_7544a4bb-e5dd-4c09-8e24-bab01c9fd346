using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;
using System.Collections.Generic;
using MHXP.UI.Carousel;
using System;
using MHXP.TabletMode;

public class UImangerProjectLevel : MonoBehaviour
{

    [SerializeField] GameObject loadigPanel;
    [SerializeField] GameObject hotspotPanel;
    [SerializeField] GameObject screenSaverGameobject;

    [Header("Chat Agent Panel")]
    [SerializeField] ChatAgentKiosk chatAgentKiosk;

    [Header("Game Panel")]
    [SerializeField] GameObject gamePanel;
    [SerializeField] Button viewsButton;
    [SerializeField] Button backButton;
    [SerializeField] GameObject sessionControlsPanel;

    [Header("Hotspot Info Panel")]
    [SerializeField] GameObject hotspotInfoPanel;
    [SerializeField] GameObject hotspotButtonsPanel;
    [SerializeField] GameObject hotspotInfoButtonPrefab;
    [SerializeField] Transform hotspotInfoButtonContainer;
    [SerializeField] Button showUnitButton;

    [Header("Left Hamburger")]
    [SerializeField] RectTransform leftPanelTransform;
    [SerializeField] Button leftHamburgerButton;
    [SerializeField] Vector2 leftPanelOpenPosition;
    [SerializeField] Vector2 leftPanelClosePosition;
    [SerializeField] Sprite hamburgerIcon;
    [SerializeField] Sprite closeIcon;
    [SerializeField] Transform LeftPanelButtonsParent;

    [Header("Right Hamburger")]
    [SerializeField] RectTransform rightPanelTransform;
    [SerializeField] Button rightHamburgerButton;
    [SerializeField] Vector2 rightPanelOpenPosition;
    [SerializeField] Vector2 rightPanelClosePosition;
    [SerializeField] Transform rightPanelContentParent;
    [SerializeField] GameObject ImageEmptySpacePrefab;
    [SerializeField] GameObject dropdownPrefab;
    [SerializeField] GameObject aminityButtonPrefab;

    [Header("Bottom Panel")]
    [SerializeField] RectTransform bottomPanelTransform;
    [SerializeField] RectTransform bottomPanelParent;
    [SerializeField] GameObject unitInfoButtonPrefab;
    [SerializeField] Vector2 bottomPanelOpenPosition;
    [SerializeField] Vector2 bottomPanelClosePosition;
    [SerializeField] GameObject colatralImageButton;

    [Header("Location Content")]
    [SerializeField] GameObject locationButtonPrefab;

    [Header("Renders")]
    [SerializeField] RawImage renderImage;
    [SerializeField] GameObject renderImageParent;

    [SerializeField] AnimationCurve curve;

    bool shouldCallScreenSaver = false;
    [SerializeField] float longPressDuration = 1f;

    AppController appController;
    AppController AppController => appController ??= FindFirstObjectByType<AppController>();

    TouchOrbitCamera touchOrbitCamera;
    TouchOrbitCamera TouchOrbitCamera => touchOrbitCamera ??= FindFirstObjectByType<TouchOrbitCamera>();

    CarouselManager carouselManager;
    CarouselManager CarouselManager => carouselManager ??=
        hotspotInfoPanel.GetComponentInChildren<CarouselManager>();

    Image SelectedButton = null;

    List<TMP_Dropdown> unitDropdowns = new List<TMP_Dropdown>();

    enum LeftPanelState
    {
        Location,
        MasterPlan,
        Towers,
        Animities,
        Units,
        Misclanious
    }

    LeftPanelState currentLeftPanelState = LeftPanelState.Location;

    private void Start()
    {
        loadigPanel.SetActive(false);
    }

    public void SetSartUIState()
    {
        backButton.gameObject.SetActive(false);
        viewsButton.gameObject.SetActive(true);
        hotspotInfoPanel.SetActive(false);
        hotspotButtonsPanel.SetActive(false);
    }

    public void SetLoaging(bool enable)
    {
        loadigPanel.SetActive(enable);
    }

    public void HideHotspotData()
    {
        hotspotInfoPanel.SetActive(false);
        hotspotButtonsPanel.SetActive(false);
        backButton.gameObject.SetActive(false);
        viewsButton.gameObject.SetActive(true);
        showUnitButton.gameObject.SetActive(false);
        foreach (Transform child in hotspotInfoButtonContainer)
        {
            Destroy(child.gameObject);
        }

        TouchOrbitCamera.ReturnToOrbitOverTime();
    }

    public void ShowHotspotData(Transform spot)
    {
        ClearBottomPanel();

        var towerUnitInfoData = spot.GetComponent<HotspotDataHolder>().towers[0];
        foreach (var flat in towerUnitInfoData.flats)
        {
            GameObject buttonObj = Instantiate(unitInfoButtonPrefab, bottomPanelParent);
            TextMeshProUGUI buttonText = buttonObj.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
                buttonText.text = flat.AppartmentName;
            Button button = buttonObj.GetComponent<Button>();
            if (button != null)
            {
                button.onClick.RemoveAllListeners();
                button.onClick.AddListener(() =>
                {
                    Debug.Log("Clicked on the appartment button");
                });
            }
        }
    }

    //private void SetCarousalMedia (HotspotDataHolder.AppartmentDetails flat)
    //{
    //    List<System.Object> Media = new List<System.Object>();
    //    if (!string.IsNullOrEmpty(flat.AppartmentDescription))
    //        Media.Add(flat.AppartmentDescription);

    //    foreach (var render in flat.renders)
    //    {
    //        Media.Add(render);
    //    }

    //    foreach (var video in flat.videoclips)
    //    {
    //        Media.Add(video);
    //    }

    //    CarouselManager.LoadMedia(Media);
    //}

    public void OnClickCloseImageShowcase()
    {
        renderImageParent.SetActive(false);
    }

    public void ShowProjectOverview()
    {
        hotspotPanel.transform.parent.gameObject.SetActive(false);
        viewsButton.gameObject.SetActive(false);
        backButton.gameObject.SetActive(true);
    }

    public void ShowTowerPlans()
    {
        hotspotPanel.transform.parent.gameObject.SetActive(false);
        viewsButton.gameObject.SetActive(false);
        backButton.gameObject.SetActive(true);
    }

    public void OnClickViews()
    {
        hotspotPanel.SetActive(!hotspotPanel.activeSelf);
    }

    public void OnClickShowUnit()
    {
        AppController.ShowUnit();
    }

    public void OnPointerDownScreenSaver()
    {
        StartCoroutine(LongPressScreenSaverRoutine());
    }

    private IEnumerator LongPressScreenSaverRoutine()
    {
        shouldCallScreenSaver = true;
        yield return new WaitForSeconds(longPressDuration);

        if (shouldCallScreenSaver)
        {
            screenSaverGameobject.SetActive(false);
            SetSartUIState();
            shouldCallScreenSaver = false;
        }
    }

    public void OnPointerUpScreenSaver()
    {
        shouldCallScreenSaver = false;
    }

    #region LeftPanelButtons 

    void OnSelectButton()
    {
        foreach (Transform child in LeftPanelButtonsParent)
        {
            if (child.GetComponent<Button>() != null)
            {
                child.GetComponent<Image>().color = Color.white;
            }
        }
    }

    void ClearRightPanelConnent(LeftPanelState newState)
    {
        //if (currentLeftPanelState != newState)
        {
            foreach (Transform child in rightPanelContentParent)
            {
                Destroy(child.gameObject);
            }
            currentLeftPanelState = newState;
        }
    }

    void ClearBottomPanel()
    {
        foreach (Transform child in bottomPanelParent)
        {
            Destroy(child.gameObject);
        }
    }

    public void OnLocationButtonClicked(bool byAi = false)
    {
        MoveBottomRect(false);
        OnSelectButton();
        AppController.StartLocationContext();

        CesiumPOIManager cesiumPOIManager = FindFirstObjectByType<CesiumPOIManager>();
        if (cesiumPOIManager == null)
        {
            Debug.Log("CesiumPOIManager not found in the scene.");
            return;
        }

        List<String> poiSets = new List<string>();

        ClearRightPanelConnent(LeftPanelState.Location);

        foreach (var category in cesiumPOIManager.poiCategories)
        {
            poiSets.Add(category.categoryName);
        }

        if (poiSets.Count > 0)
        {
            GameObject GO = Instantiate(locationButtonPrefab, rightPanelContentParent);
            TextMeshProUGUI buttonText = GO.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                buttonText.text = "All POI";
            }

            Button locationButton = GO.GetComponent<Button>();
            locationButton.onClick.RemoveAllListeners();
            locationButton.onClick.AddListener(() =>
            {
                cesiumPOIManager.ShowAllPoi();
            });

            foreach (var category in poiSets)
            {
                GO = Instantiate(locationButtonPrefab, rightPanelContentParent);
                buttonText = GO.GetComponentInChildren<TextMeshProUGUI>();
                if (buttonText != null)
                {
                    buttonText.text = category;
                }

                locationButton = GO.GetComponent<Button>();
                locationButton.onClick.RemoveAllListeners();
                locationButton.onClick.AddListener(() =>
                {
                    cesiumPOIManager.ShowCategory(category);
                });
            }

            Invoke("CallRightFirstButton", 0.1f);
        }
        if(!byAi)
        {
            chatAgentKiosk.UpdateAgentContext_OnNewLocation("Location");
        }
    }

    void CallRightFirstButton()
    {
        var button = rightPanelContentParent.GetChild(0).GetComponent<Button>();
        if (button != null)
        {
            button.onClick.Invoke();
        }

    }

    public void OnMasterPlansButtonClicked()
    {
        MoveBottomRect(false);
        OnSelectButton();
        ClearRightPanelConnent(LeftPanelState.MasterPlan);

        AppController.ResetOrbitCamera();
    }

    public void OnTowersButtonClicked(bool byAi = false)
    {
        OnSelectButton();
        ClearRightPanelConnent(LeftPanelState.Towers);

        foreach (string towerName in AppController.DataController.TowerNames)
        {
            GameObject GO = Instantiate(locationButtonPrefab, rightPanelContentParent);
            TextMeshProUGUI buttonText = GO.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                buttonText.text = towerName;
            }
            Button locationButton = GO.GetComponent<Button>();
            locationButton.onClick.RemoveAllListeners();
            locationButton.onClick.AddListener(() =>
            {
                AppController.OnClickTower(towerName);
                MoveBottomRect(true);
            });
        }
        if(!byAi)
        {
            chatAgentKiosk.UpdateAgentContext_OnNewLocation("Towers");
        }
    }

    public void OnAnimitiesButtonClicked(bool byAi = false)
    {
        MoveBottomRect(false);
        ClearRightPanelConnent(LeftPanelState.Animities);
        OnSelectButton();

        foreach (var amanity in AppController.DataController.allAmanities)
        {
            GameObject GO = Instantiate(aminityButtonPrefab, rightPanelContentParent);
            TextMeshProUGUI buttonText = GO.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                buttonText.text = amanity.amenityName;
            }
            Button locationButton = GO.GetComponent<Button>();
            locationButton.onClick.RemoveAllListeners();
            locationButton.onClick.AddListener(() =>
            {
                Debug.Log("Clicked on Amenity: " + amanity.amenityName);
                ClearBottomPanel();
                MoveBottomRect(true);

                foreach (Sprite sprite in amanity.aminityImages)
                {
                    GameObject imageGO = Instantiate(colatralImageButton, bottomPanelParent);
                    Image ButtonImage = imageGO.GetComponent<Image>();
                    if (ButtonImage != null)
                    {
                        ButtonImage.sprite = sprite;
                    }

                    Button button = imageGO.GetComponent<Button>();
                    TextMeshProUGUI buttonText = imageGO.GetComponentInChildren<TextMeshProUGUI>();
                    if (buttonText != null)
                    {
                        buttonText.gameObject.SetActive(false);
                    }
                    button.onClick.RemoveAllListeners();
                    button.onClick.AddListener(() =>
                    {
                        renderImage.texture = sprite.texture;
                        renderImageParent.SetActive(true);
                    });
                }

                if (!string.IsNullOrEmpty(amanity.sceneName))
                {
                    GameObject availableButtonGO = Instantiate(colatralImageButton, bottomPanelParent);
                    Button availableButton = availableButtonGO.GetComponent<Button>();
                    TextMeshProUGUI availableButtonText = availableButtonGO.GetComponentInChildren<TextMeshProUGUI>();
                    if (availableButtonText != null)
                    {
                        availableButtonText.text = "Visit";
                    }
                    availableButton.onClick.RemoveAllListeners();
                    availableButton.onClick.AddListener(() =>
                    {
                        AppController.sceneToGoTo = amanity.sceneName;
                        AppController.ShowUnit();
                    });
                }
            });
        }
        if(!byAi)
        {
            chatAgentKiosk.UpdateAgentContext_OnNewLocation("Amenities");
        }

    }

    public void OnUnitsButtonClicked(bool byAi = false)
    {
        MoveBottomRect(false);
        ClearRightPanelConnent(LeftPanelState.Units);
        OnSelectButton();

        foreach (KeyValuePair<string, List<UnitTypeData>> kvp in AppController.DataController.groupedUnits)
        {
            string bedroomType = kvp.Key;
            List<UnitTypeData> units = kvp.Value;

            // Add a dropdown here 
            GameObject dropDpwnGO = Instantiate(dropdownPrefab, rightPanelContentParent);

            TextMeshProUGUI DropdownName = dropDpwnGO.transform.GetChild(1)
                .gameObject.GetComponent<TextMeshProUGUI>();
            DropdownName.text = bedroomType;

            TMP_Dropdown unitTypeDropDown = dropDpwnGO.transform.GetChild(0).
                gameObject.GetComponent<TMP_Dropdown>();

            unitTypeDropDown.ClearOptions();
            unitTypeDropDown.options.Add(new TMP_Dropdown.OptionData("Select Unit"));
            foreach (UnitTypeData unit in units)
            {
                //dropdownOptions.Add(new TMP_Dropdown.OptionData(unit.UnitName));
                unitTypeDropDown.options.Add(new TMP_Dropdown.OptionData(unit.UnitName));
            }

            unitTypeDropDown.onValueChanged.RemoveAllListeners();
            unitTypeDropDown.onValueChanged.AddListener(
                (int optionIndex) =>
                {
                    //ResetAllUnitDropdowns();
                    ClearBottomPanel();
                    Image image = unitTypeDropDown.gameObject.GetComponent<Image>();
                    if (image != null)
                    {
                        image.color = Color.green;
                    }
                    MoveBottomRect(true);
                    // Set image buttons here

                    foreach (var unit in units)
                    {
                        if (unit.UnitName == unitTypeDropDown.options[optionIndex].text)
                        {
                            foreach (Sprite render in unit.sprites)
                            {
                                GameObject buttonGO = Instantiate(colatralImageButton, bottomPanelParent);

                                buttonGO.GetComponent<Image>().sprite = render;
                                Button button = buttonGO.GetComponent<Button>();

                                TextMeshProUGUI buttonText = buttonGO.GetComponentInChildren<TextMeshProUGUI>();
                                if (buttonText != null)
                                {
                                    buttonText.gameObject.SetActive(false);
                                }

                                if (button != null)
                                {
                                    button.onClick.RemoveAllListeners();
                                    button.onClick.AddListener(
                                        () =>
                                        {
                                            Debug.Log("Clicked on Button");
                                            renderImage.texture = render.texture;
                                            renderImageParent.SetActive(true);
                                        });
                                }
                            }

                            if (bottomPanelParent.childCount > 0)
                            {
                                GameObject availableButtonGO = Instantiate(colatralImageButton, bottomPanelParent);
                                Button availableButton = availableButtonGO.GetComponent<Button>();
                                TextMeshProUGUI availableButtonText = availableButtonGO.GetComponentInChildren<TextMeshProUGUI>();
                                if (availableButtonText != null)
                                {
                                    availableButtonText.text = "Available";
                                }
                                availableButton.onClick.RemoveAllListeners();
                                availableButton.onClick.AddListener(() =>
                                {
                                    AppController.sceneToGoTo = unit.sceneName;
                                    AppController.ShowUnit();

                                });
                            }
                            if (bottomPanelParent.childCount == 0)
                                MoveBottomRect(false);
                        }
                    }

                });

            unitDropdowns.Add(unitTypeDropDown);
        }

        if(!byAi)
        {
            chatAgentKiosk.UpdateAgentContext_OnNewLocation("Units");
        }

    }

    private void ResetAllUnitDropdowns()
    {
        foreach (var dropdown in unitDropdowns)
        {
            dropdown.value = 0; // Reset to first option
            Image image = dropdown.gameObject.GetComponent<Image>();
            if (image != null)
            {
                image.color = Color.white;
            }
        }
    }

    public void OnMisclaniousButtonClicked()
    {
        MoveBottomRect(false);
        OnSelectButton();
        ClearRightPanelConnent(LeftPanelState.Misclanious);
    }

    public void OnChangedZoomLevel()
    {
        MoveBottomRect(false);
    }

    public void OnClickBackButton()
    {
        MoveBottomRect(false);
        ShowProjectOverview();
        TouchOrbitCamera.StartAutoRotation();
    }

    #endregion LeftPanelButtons


    #region HamburgerPanels

    public void OnClickLeftHamburger()
    {
        StartCoroutine(MoveRectTransform(leftPanelTransform,
            leftPanelTransform.anchoredPosition == leftPanelOpenPosition ? leftPanelClosePosition : leftPanelOpenPosition, 1f,
            () =>
            {
                var sprite = leftPanelTransform.anchoredPosition == leftPanelOpenPosition ? closeIcon : hamburgerIcon;
                leftHamburgerButton.transform.GetChild(0).GetComponent<Image>().sprite = sprite;
            }));
    }

    public void OnClickRightHamburger()
    {
        StartCoroutine(MoveRectTransform(rightPanelTransform,
            rightPanelTransform.anchoredPosition == rightPanelOpenPosition ? rightPanelClosePosition : rightPanelOpenPosition, 1f,
            () =>
            {
                var sprite = rightPanelTransform.anchoredPosition == rightPanelOpenPosition ? closeIcon : hamburgerIcon;
                rightHamburgerButton.transform.GetChild(0).GetComponent<Image>().sprite = sprite;
            }));

    }

    public void MoveBottomRect(bool moveUp)
    {
        Vector2 toPosition = moveUp ? bottomPanelOpenPosition : bottomPanelClosePosition;
        if (bottomPanelTransform.anchoredPosition != toPosition)
        {
            StartCoroutine(MoveRectTransform(bottomPanelTransform, toPosition, 1f,
                () =>
                {
                }));
        }
    }

    public IEnumerator MoveRectTransform(RectTransform rectTransform, Vector2 toPosition, float duration, System.Action callback)
    {
        Vector2 fromPosition = rectTransform.anchoredPosition;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            float t = elapsed / duration;
            float evaluated = curve.Evaluate(t);
            rectTransform.anchoredPosition = Vector2.LerpUnclamped(fromPosition, toPosition, evaluated);
            elapsed += Time.deltaTime;
            yield return null;
        }

        rectTransform.anchoredPosition = toPosition;
        callback?.Invoke();
    }

    #endregion HamburgerPanels
    #region SessionControls

    public void StartSessionWithAI()
    {
        StartChatAgent();
        sessionControlsPanel.SetActive(false);
    }

    public void StartSessionWithoutAI()
    {
        sessionControlsPanel.SetActive(false);
    }
    #endregion

    #region ChatAgentControls
    [ContextMenu("ChatAgent/Start Chat Agent")]
    public void StartChatAgent()
    {
        if (!chatAgentKiosk.gameObject.activeSelf)
        {
            //first time
            chatAgentKiosk.SetUIManagerProjectLevel(this);
            chatAgentKiosk.OnStateChange += (state) =>
            {
                Debug.Log($"Chat Agent AgentStateChanged: {state}");

            };
            chatAgentKiosk.gameObject.SetActive(true);
            Debug.Log("Chat Agent started.");
        }
        else
        {
            Debug.Log("Chat Agent is already enabled, starting session.");
            chatAgentKiosk.StartSession();
        }

    }

    public void StopChatAgent()
    {

        chatAgentKiosk.StopSession();
        chatAgentKiosk.gameObject.SetActive(false);
        Debug.Log("Chat Agent stopped.");


    }

    #endregion ChatAgentControls

    #region ChatAgentToolCallbacks

    public void ShowContent(string contentLabel, ActionFinishAgentTrigger finishCallback = null)
    {
        Debug.Log($"ShowContent called ! Setting current topic and bullet {contentLabel}");
        switch (contentLabel)
        {
            case "Units":
                OnUnitsButtonClicked(byAi: true);
                finishCallback?.Invoke($"Content {contentLabel} Shown, please continue.");
                break;
            case "Towers":
                OnTowersButtonClicked(byAi: true);
                finishCallback?.Invoke($"Content {contentLabel} Shown, please continue.");
                break;
            case "Location":
                OnLocationButtonClicked(byAi: true);
                finishCallback?.Invoke($"Content {contentLabel} Shown, please continue.");
                break;
            case "Amenities":
                OnAnimitiesButtonClicked(byAi: true);
                finishCallback?.Invoke($"Content {contentLabel} Shown, please continue.");
                break;
            case "GuidedTour":
               // AppController.StartGuidedTour();
                finishCallback?.Invoke($"Guided Tour has finished, please continue.");
                break;
            default:
                Debug.LogWarning($"Content {contentLabel} not recognized, please explain verbally.");
                finishCallback?.Invoke($"Content {contentLabel} Not Found, please continue.");
                break;
        }

    }
    public void HighlightAreaInVirtualTour(string contentLabel, ActionFinishAgentTrigger finishCallback = null)
    {
        Debug.Log($"HighlightAreaInVirtualTour called ! Setting current topic and bullet {contentLabel}");
        finishCallback?.Invoke($"Content {contentLabel} Shown in Virtual Tour, please continue.");
    }
    #endregion ChatAgentToolCallbacks

    private bool isAITalkingCache = false;
    public void Update()
    {
        if (isAITalkingCache != chatAgentKiosk.isAITalking)
        {
            isAITalkingCache = chatAgentKiosk.isAITalking;
            // Handle the change in AI talking state
            Debug.Log($"AI Talking AgentStateChanged: {(isAITalkingCache ? "Talking" : "Not Talking")}");
        }
    }
}
