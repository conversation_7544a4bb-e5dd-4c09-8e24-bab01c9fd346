%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8724667667391605663
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: TessellationRevealMat
  m_Shader: {fileID: 4800000, guid: 560d21286621d374f83752bef237621a, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GlowGradientTex:
        m_Texture: {fileID: 2800000, guid: 209f953fe06755846983a1cc5f9221fc, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GradientTex:
        m_Texture: {fileID: 2800000, guid: 209f953fe06755846983a1cc5f9221fc, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskTex:
        m_Texture: {fileID: 2800000, guid: 24f359e044da81d40ab2bfe430318634, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _WireTex:
        m_Texture: {fileID: 2800000, guid: e6bc209bd05297f44ac095b8cb11ba46, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 0
    - _AlphaToMask: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _CellDensity: 2
    - _CellMode: 2
    - _CellType: 0
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _CurveControlledTransition: 0.010476944
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DissolveMode: 0
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _GlowFalloff: 1
    - _GlowFlickerSpeed: 0
    - _GlowRandomPerCell: 0
    - _GlowStrength: 1
    - _MaskThreshold: 0.5
    - _MaskTiling: 5
    - _Metallic: 0
    - _NoiseScale: 0
    - _NoiseSpeed: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.005
    - _PulseSpeed: 0.1
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _RevealAmount: 0.689
    - _RevealMode: 0
    - _RevealRadius: 10
    - _ScanlineSpeed: 0
    - _ScanlineWidth: 0
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _Surface: 0
    - _Tiling: 10
    - _Transition: 0
    - _TriangleDensity: 6
    - _UseTriangleGrid: 4
    - _VertexOffset: 0
    - _VertexPushDirection: 1
    - _WorkflowMode: 1
    - _XRMotionVectorsPass: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 0, g: 0, b: 0, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _GlowColor: {r: 0, g: 0.5053909, b: 0.8018868, a: 1}
    - _RevealOrigin: {r: 13.39, g: 4.32, b: -0.08, a: 0}
    - _RevealOrigin0: {r: -1.421, g: 1.401, b: 13.44, a: 0}
    - _RevealOrigin1: {r: -2.781, g: 0.08, b: 12.557, a: 0}
    - _RevealOrigin10: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin11: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin12: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin13: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin14: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin15: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin16: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin17: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin18: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin19: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin2: {r: -5.058, g: 1.805, b: 13.462, a: 0}
    - _RevealOrigin20: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin21: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin22: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin23: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin3: {r: 14.41, g: 0.05, b: -10.2, a: 0}
    - _RevealOrigin4: {r: 18.63, g: -1.83, b: -8.93, a: 0}
    - _RevealOrigin5: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin6: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin7: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin8: {r: 0, g: 0, b: 0, a: 0}
    - _RevealOrigin9: {r: 0, g: 0, b: 0, a: 0}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
