using UnityEngine;
using System;
using System.Collections;

public class RevealTessalation : MonoBehaviour
{
    [Header("Shader Control")]
    public Material[] materials; // ✅ Now supports multiple materials
    public Transform[] revealOrigins = new Transform[10];

    [Header("Radius Control")]
    public float revealSpeed = 1f;
    public float maxRadius = 10f;
    public AnimationCurve revealCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    [Header("Dissolve Control")]
    public float dissolveDelay = 0f;
    public float dissolveDuration = 2f;
    public AnimationCurve dissolveCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    private float revealRadius = 0f;
    private Coroutine currentRoutine;
    private Coroutine dissolveRoutine;

    void Start()
    {
        UpdateOrigins();
        SetMaterialsFloat("_RevealRadius", revealRadius);
        SetMaterialsFloat("_Transition", 0f);
        StartReveal();
        StartDelayedDissolve(dissolveDelay, dissolveDuration);
    }

    void UpdateOrigins()
    {
        for (int i = 0; i < revealOrigins.Length && i < 10; i++)
        {
            if (revealOrigins[i] != null)
            {
                Vector3 pos = revealOrigins[i].position;
                SetMaterialsVector($"_RevealOrigin{i}", new Vector4(pos.x, pos.y, pos.z, 0));
            }
        }
    }

    IEnumerator RevealCoroutine(Action onComplete)
    {
        float timer = 0f;
        float duration = maxRadius / revealSpeed;

        while (timer < duration)
        {
            float t = timer / duration;
            float curvedT = revealCurve.Evaluate(t);
            revealRadius = Mathf.Lerp(0f, maxRadius, curvedT);
            SetMaterialsFloat("_RevealRadius", revealRadius);
            timer += Time.deltaTime;
            yield return null;
        }

        revealRadius = maxRadius;
        SetMaterialsFloat("_RevealRadius", revealRadius);
        onComplete?.Invoke();
    }

    IEnumerator CollapseCoroutine(Action onComplete)
    {
        float timer = 0f;
        float duration = maxRadius / revealSpeed;

        while (timer < duration)
        {
            float t = timer / duration;
            float curvedT = revealCurve.Evaluate(t);
            revealRadius = Mathf.Lerp(maxRadius, 0f, curvedT);
            SetMaterialsFloat("_RevealRadius", revealRadius);
            timer += Time.deltaTime;
            yield return null;
        }

        revealRadius = 0f;
        SetMaterialsFloat("_RevealRadius", revealRadius);
        onComplete?.Invoke();
    }

    public void StartReveal(Action onComplete = null)
    {
        if (currentRoutine != null) StopCoroutine(currentRoutine);
        UpdateOrigins();
        currentRoutine = StartCoroutine(RevealCoroutine(onComplete));
    }

    public void StartCollapse(Action onComplete = null)
    {
        if (currentRoutine != null) StopCoroutine(currentRoutine);
        currentRoutine = StartCoroutine(CollapseCoroutine(onComplete));
    }

    public void ResetReveal()
    {
        if (currentRoutine != null) StopCoroutine(currentRoutine);
        if (dissolveRoutine != null) StopCoroutine(dissolveRoutine);
        revealRadius = 0f;
        SetMaterialsFloat("_RevealRadius", revealRadius);
        SetMaterialsFloat("_Transition", 0f);
    }

    public void StartDissolve(Action onComplete = null)
    {
        if (dissolveRoutine != null) StopCoroutine(dissolveRoutine);
        dissolveRoutine = StartCoroutine(DissolveCoroutine(dissolveDuration, onComplete));
    }

    public void StartDelayedDissolve(float delaySeconds, float durationSeconds, Action onComplete = null)
    {
        if (dissolveRoutine != null) StopCoroutine(dissolveRoutine);
        dissolveRoutine = StartCoroutine(DelayedDissolveCoroutine(delaySeconds, durationSeconds, onComplete));
    }

    private IEnumerator DissolveCoroutine(float duration, Action onComplete)
    {
        float timer = 0f;
        while (timer < duration)
        {
            float t = timer / duration;
            float curvedT = dissolveCurve.Evaluate(t);
            SetMaterialsFloat("_Transition", curvedT);
            timer += Time.deltaTime;
            yield return null;
        }

        SetMaterialsFloat("_Transition", 1f);
        onComplete?.Invoke();
    }

    private IEnumerator DelayedDissolveCoroutine(float delay, float duration, Action onComplete)
    {
        yield return new WaitForSeconds(delay);
        yield return DissolveCoroutine(duration, onComplete);
    }

    // 🔁 UTILITY: Apply to all materials
    private void SetMaterialsFloat(string name, float value)
    {
        foreach (var mat in materials)
            mat?.SetFloat(name, value);
    }

    private void SetMaterialsVector(string name, Vector4 value)
    {
        foreach (var mat in materials)
            mat?.SetVector(name, value);
    }
}
