using System.Collections.Generic;
using UnityEngine;

namespace MHXP.core.SceneManagement
{
    [DisallowMultipleComponent]
    public class ToggleManager_Auto : MonoBehaviour
    {
        [Header("Assign all GameObjects named same as scenes")]
        public List<GameObject> sceneBasedObjects;

        [Tooltip("LoaderInit should exist in the Loader scene")]
        public LoaderInit loaderInit;

        [Header("XR Rig to move (assign your custom rig here)")]
        public Transform xrRig;

        private void OnEnable()
        {
            TrySubscribe();
        }

        private void OnDisable()
        {
            if (loaderInit != null)
                loaderInit.OnNextSceneSet -= OnNextSceneSet;
        }

        private void Start()
        {
            TrySubscribe();
            // If nextScene is already set, toggle immediately
            if (loaderInit != null && loaderInit.GetNextScene() != null)
                OnNextSceneSet(loaderInit.GetNextScene());
        }

        private void TrySubscribe()
        {
            if (loaderInit == null)
                loaderInit = FindAnyObjectByType<LoaderInit>();
            if (loaderInit != null)
                loaderInit.OnNextSceneSet += OnNextSceneSet;
        }

        public void ManualRefresh()
        {
            if (loaderInit == null)
                loaderInit = FindAnyObjectByType<LoaderInit>();
            if (loaderInit != null && loaderInit.GetNextScene() != null)
                OnNextSceneSet(loaderInit.GetNextScene());
        }

        private void OnNextSceneSet(SceneData nextScene)
        {
            if (nextScene == null || string.IsNullOrEmpty(nextScene.sceneName))
            {
                Debug.LogWarning("[ToggleManager_Auto] Upcoming scene name is empty or nextScene is null.");
                return;
            }
            string upcomingSceneName = nextScene.sceneName;
            Debug.Log($"[ToggleManager_Auto] Upcoming Scene: {upcomingSceneName}");
            int activated = 0, deactivated = 0;
            GameObject activatedObj = null;
            foreach (GameObject obj in sceneBasedObjects)
            {
                if (obj == null) continue;
                bool shouldBeActive = obj.name == upcomingSceneName;
                obj.SetActive(shouldBeActive);
                if (shouldBeActive)
                {
                    activated++;
                    activatedObj = obj;
                }
                else deactivated++;
                Debug.Log($"[ToggleManager_Auto] {obj.name} → {(shouldBeActive ? "Activated" : "Deactivated")}");
            }
            Debug.Log($"[ToggleManager_Auto] Done. Activated: {activated}, Deactivated: {deactivated}");

            // Move XR Rig instantly to activated object's position/rotation
            if (xrRig != null && activatedObj != null)
            {
                xrRig.position = activatedObj.transform.position;
                xrRig.rotation = activatedObj.transform.rotation;
                Debug.Log($"[ToggleManager_Auto] XR Rig moved to {activatedObj.name} position and rotation.");
            }
            else if (xrRig == null)
            {
                Debug.LogWarning("[ToggleManager_Auto] XR Rig reference is not assigned in the inspector.");
            }
        }
    }
}
