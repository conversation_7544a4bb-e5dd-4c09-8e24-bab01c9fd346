Shader "MHXP_Custom/Tessellation_Reveal"
{
    Properties
    {
        _WireTex("Tessellation Texture", 2D) = "white" {}
        _BaseColor("Base Color", Color) = (1,1,1,1)
        _GlowColor("Glow Color", Color) = (0,1,1,1)
        _GlowStrength("Glow Strength", Float) = 2
        _Tiling("Tiling", Float) = 10
        _RevealRadius("Reveal Radius", Range(0, 10)) = 0
        _Transition("Global Transition Control", Range(0, 1)) = 0
        [Enum(Reveal, 0, Dissolve, 1)] _DissolveMode("Dissolve Mode", Float) = 0

        // 24 Reveal Origins
        _RevealOrigin0("Reveal Origin 0", Vector) = (0,0,0,0)
        _RevealOrigin1("Reveal Origin 1", Vector) = (0,0,0,0)
        _RevealOrigin2("Reveal Origin 2", Vector) = (0,0,0,0)
        _RevealOrigin3("Reveal Origin 3", Vector) = (0,0,0,0)
        _RevealOrigin4("Reveal Origin 4", Vector) = (0,0,0,0)
        _RevealOrigin5("Reveal Origin 5", Vector) = (0,0,0,0)
        _RevealOrigin6("Reveal Origin 6", Vector) = (0,0,0,0)
        _RevealOrigin7("Reveal Origin 7", Vector) = (0,0,0,0)
        _RevealOrigin8("Reveal Origin 8", Vector) = (0,0,0,0)
        _RevealOrigin9("Reveal Origin 9", Vector) = (0,0,0,0)
        _RevealOrigin10("Reveal Origin 10", Vector) = (0,0,0,0)
        _RevealOrigin11("Reveal Origin 11", Vector) = (0,0,0,0)
        _RevealOrigin12("Reveal Origin 12", Vector) = (0,0,0,0)
        _RevealOrigin13("Reveal Origin 13", Vector) = (0,0,0,0)
        _RevealOrigin14("Reveal Origin 14", Vector) = (0,0,0,0)
        _RevealOrigin15("Reveal Origin 15", Vector) = (0,0,0,0)
        _RevealOrigin16("Reveal Origin 16", Vector) = (0,0,0,0)
        _RevealOrigin17("Reveal Origin 17", Vector) = (0,0,0,0)
        _RevealOrigin18("Reveal Origin 18", Vector) = (0,0,0,0)
        _RevealOrigin19("Reveal Origin 19", Vector) = (0,0,0,0)
        _RevealOrigin20("Reveal Origin 20", Vector) = (0,0,0,0)
        _RevealOrigin21("Reveal Origin 21", Vector) = (0,0,0,0)
        _RevealOrigin22("Reveal Origin 22", Vector) = (0,0,0,0)
        _RevealOrigin23("Reveal Origin 23", Vector) = (0,0,0,0)

        _ScanlineWidth("Scanline Width", Float) = 0.2
        _ScanlineSpeed("Scanline Speed", Float) = 1
        _CellDensity("Cell Density", Float) = 5
        _UseTriangleGrid("Use Triangle Grid Mode", Float) = 0
        _VertexOffset("Vertex Offset", Float) = 0.01
        [Enum(Inward, 0, Outward, 1)] _VertexPushDirection("Offset Direction", Float) = 1
    }

    SubShader
    {
        Tags { "RenderType"="Transparent" "Queue"="Transparent" }
        ZWrite On
        Blend SrcAlpha OneMinusSrcAlpha
        Cull Back

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            sampler2D _WireTex;
            float4 _WireTex_ST;

            float4 _BaseColor, _GlowColor;
            float _GlowStrength, _Tiling;
            float _RevealRadius, _Transition, _DissolveMode;

            float4 _RevealOrigin0, _RevealOrigin1, _RevealOrigin2, _RevealOrigin3, _RevealOrigin4;
            float4 _RevealOrigin5, _RevealOrigin6, _RevealOrigin7, _RevealOrigin8, _RevealOrigin9;
            float4 _RevealOrigin10, _RevealOrigin11, _RevealOrigin12, _RevealOrigin13, _RevealOrigin14;
            float4 _RevealOrigin15, _RevealOrigin16, _RevealOrigin17, _RevealOrigin18, _RevealOrigin19;
            float4 _RevealOrigin20, _RevealOrigin21, _RevealOrigin22, _RevealOrigin23;

            float _ScanlineWidth, _ScanlineSpeed, _CellDensity, _UseTriangleGrid;
            float _VertexOffset, _VertexPushDirection;

            float2 hash2(float2 p)
            {
                p = float2(dot(p, float2(127.1, 311.7)), dot(p, float2(269.5, 183.3)));
                return frac(sin(p) * 43758.5453);
            }

            float voronoi(float2 uv)
            {
                float2 g = floor(uv);
                float2 f = frac(uv);
                float minDist = 1.0;

                for (int y = -1; y <= 1; y++)
                {
                    for (int x = -1; x <= 1; x++)
                    {
                        float2 lattice = float2(x, y);
                        float2 cellCenter = hash2(g + lattice);
                        float2 diff = lattice + cellCenter - f;
                        float dist = dot(diff, diff);
                        minDist = min(minDist, dist);
                    }
                }
                return minDist;
            }

            float triangleGridMask(float2 uv, float density)
            {
                float2 grid = floor(uv * density);
                float2 f = frac(uv * density);
                if (fmod(grid.y, 2.0) > 0.5)
                    f.x = 1.0 - f.x;
                return max(f.x, f.y);
            }

            struct Attributes
            {
                float4 positionOS : POSITION;
                float2 uv : TEXCOORD0;
                float3 normal : NORMAL;
            };

            struct Varyings
            {
                float4 positionHCS : SV_POSITION;
                float3 worldPos : TEXCOORD0;
                float2 uv : TEXCOORD1;
            };

            Varyings vert(Attributes IN)
            {
                Varyings OUT;
                float3 normalOS = normalize(IN.normal);
                float directionSign = (_VertexPushDirection > 0.5) ? 1.0 : -1.0;
                float3 offsetPosOS = IN.positionOS.xyz + normalOS * _VertexOffset * directionSign;
                float3 worldPos = TransformObjectToWorld(offsetPosOS);

                OUT.worldPos = worldPos;
                OUT.uv = IN.uv * _Tiling;
                OUT.positionHCS = TransformWorldToHClip(worldPos);
                return OUT;
            }

            half4 frag(Varyings IN) : SV_Target
            {
                float3 worldPos = IN.worldPos;
                float2 uv = IN.uv;

                float minDist = distance(worldPos, _RevealOrigin0.xyz);

                float3 origins[24] = {
                    _RevealOrigin0.xyz, _RevealOrigin1.xyz, _RevealOrigin2.xyz, _RevealOrigin3.xyz,
                    _RevealOrigin4.xyz, _RevealOrigin5.xyz, _RevealOrigin6.xyz, _RevealOrigin7.xyz,
                    _RevealOrigin8.xyz, _RevealOrigin9.xyz, _RevealOrigin10.xyz, _RevealOrigin11.xyz,
                    _RevealOrigin12.xyz, _RevealOrigin13.xyz, _RevealOrigin14.xyz, _RevealOrigin15.xyz,
                    _RevealOrigin16.xyz, _RevealOrigin17.xyz, _RevealOrigin18.xyz, _RevealOrigin19.xyz,
                    _RevealOrigin20.xyz, _RevealOrigin21.xyz, _RevealOrigin22.xyz, _RevealOrigin23.xyz
                };

                [unroll]
                for (int i = 1; i < 24; i++)
                {
                    float d = distance(worldPos, origins[i]);
                    minDist = min(minDist, d);
                }

                float cellMask = (_UseTriangleGrid > 0.5)
                    ? triangleGridMask(uv, _CellDensity)
                    : voronoi(uv * _CellDensity);

                float revealMask = smoothstep(
                    _RevealRadius - 0.02, 
                    _RevealRadius + 0.02, 
                    minDist + cellMask
                );

                float scanlineY = frac(_Time.y * _ScanlineSpeed);
                float scanlineGlow = smoothstep(scanlineY - _ScanlineWidth, scanlineY, worldPos.y) *
                                     (1 - smoothstep(scanlineY, scanlineY + _ScanlineWidth, worldPos.y));

                float wire = tex2D(_WireTex, uv).r;
                float glowMask = wire * (1 - revealMask);
                float glow = saturate(glowMask + scanlineGlow);
                float3 glowColor = _GlowColor.rgb * _GlowStrength;
                float3 finalColor = lerp(_BaseColor.rgb, glowColor, glow);

                float visibility = (_DissolveMode > 0.5)
                    ? step(_Transition, revealMask)
                    : step(revealMask, _Transition);

                return float4(finalColor, visibility * (1.0 - _Transition));
            }

            ENDHLSL
        }
    }
}
