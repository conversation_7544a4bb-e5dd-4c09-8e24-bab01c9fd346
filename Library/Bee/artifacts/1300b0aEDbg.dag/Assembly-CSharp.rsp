-target:library
-out:"Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.ref.dll"
-define:UNITY_6000_1_4
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_ACCESSIBILITY
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_ANDROID
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:PLATFORM_EXTENDS_VULKAN_PIPELINE_CACHE
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:PLATFORM_HAS_ADDITIONAL_API_CHECKS
-define:PLATFORM_HAS_GRAPHICS_JOBS_SUPPORT_CHECK_OVERRIDE
-define:PLATFORM_HAS_BUGGY_MSAA_RESOLVE
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_OSX
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:STARTER_ASSETS_PACKAGES_CHECKED
-define:USE_INPUT_SYSTEM_POSE_CONTROL
-define:FUSION_WEAVER
-define:FUSION2
-define:BAKERY_INCLUDED
-define:BAKERY_NOREIMPORT
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"Assets/Packages/Microsoft.Bcl.AsyncInterfaces.9.0.1/lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll"
-r:"Assets/Packages/Microsoft.Bcl.TimeProvider.9.0.1/lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll"
-r:"Assets/Packages/Microsoft.Extensions.Caching.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll"
-r:"Assets/Packages/Microsoft.Extensions.Caching.Memory.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll"
-r:"Assets/Packages/Microsoft.Extensions.Configuration.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll"
-r:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll"
-r:"Assets/Packages/Microsoft.Extensions.DependencyInjection.9.0.1/lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll"
-r:"Assets/Packages/Microsoft.Extensions.DependencyInjection.Abstractions.9.0.1/lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll"
-r:"Assets/Packages/Microsoft.Extensions.Http.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Http.dll"
-r:"Assets/Packages/Microsoft.Extensions.Logging.9.0.1/lib/netstandard2.1/Microsoft.Extensions.Logging.dll"
-r:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll"
-r:"Assets/Packages/Microsoft.Extensions.Options.9.0.1/lib/netstandard2.1/Microsoft.Extensions.Options.dll"
-r:"Assets/Packages/Microsoft.Extensions.Primitives.9.0.1/lib/netstandard2.0/Microsoft.Extensions.Primitives.dll"
-r:"Assets/Packages/PostHog.1.0.5/lib/netstandard2.1/PostHog.dll"
-r:"Assets/Packages/System.ComponentModel.Annotations.5.0.0/lib/netstandard2.1/System.ComponentModel.Annotations.dll"
-r:"Assets/Packages/System.Diagnostics.DiagnosticSource.9.0.1/lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll"
-r:"Assets/Packages/System.IO.Pipelines.9.0.1/lib/netstandard2.0/System.IO.Pipelines.dll"
-r:"Assets/Packages/System.Net.Http.Json.9.0.1/lib/netstandard2.0/System.Net.Http.Json.dll"
-r:"Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll"
-r:"Assets/Packages/System.Text.Encodings.Web.9.0.1/lib/netstandard2.0/System.Text.Encodings.Web.dll"
-r:"Assets/Packages/System.Text.Json.9.0.1/lib/netstandard2.0/System.Text.Json.dll"
-r:"Assets/Packages/System.Threading.Channels.9.0.1/lib/netstandard2.1/System.Threading.Channels.dll"
-r:"Assets/Photon/Fusion/Assemblies/Fusion.Common.dll"
-r:"Assets/Photon/Fusion/Assemblies/Fusion.Log.dll"
-r:"Assets/Photon/Fusion/Assemblies/Fusion.Realtime.dll"
-r:"Assets/Photon/Fusion/Assemblies/Fusion.Runtime.dll"
-r:"Assets/Photon/Fusion/Assemblies/Fusion.Sockets.dll"
-r:"Assets/Photon/Fusion/Plugins/NanoSockets/NanoSockets/Dynamic/NanoSockets.dll"
-r:"Assets/Photon/PhotonLibs/netstandard2.0/Photon3Unity3D.dll"
-r:"Assets/Photon/PhotonLibs/WebSocket/websocket-sharp.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AssistantCoreSDKEditor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AssistantCoreSDKRuntime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AssistantVoiceCommandCommon.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AssistantVoiceCommandCommon.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Autodesk.Fbx.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Autodesk.Fbx.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/BakeryEditorAssembly.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/BakeryRuntimeAssembly.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/CesiumEditor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/CesiumRuntime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Cognitive3D.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Fusion.Unity.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Fusion.Unity.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.Net.endel.nativewebsocket.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.Voice.Hub.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.Voice.Hub.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.Voice.VSDKHub.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.VoiceSDK.Mic.Common.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.VoiceSDK.Mic.Other.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.VoiceSDK.Mic.WebGL.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.Wit.Composer.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.Wit.Composer.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.Wit.Dictation.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.Wit.Dictation.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.WitAi.Conduit.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.WitAi.Conduit.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.WitAi.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.WitAI.Lib.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.WitAi.Lib.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.WitAi.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.WitAi.TTS.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.WitAi.TTS.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.Audio.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.Audio.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.BuildingBlocks.DepthAPI.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.BuildingBlocks.DepthAPI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.BuildingBlocks.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.BuildingBlocks.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.Editor.Callbacks.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.Editor.PlayCompanion.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.Editor.StatusMenu.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.Editor.Tags.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.Editor.UserInterface.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.EnvironmentDepth.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.EnvironmentDepth.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.Guides.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.ImmersiveDebugger.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.ImmersiveDebugger.Interface.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.ImmersiveDebugger.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/meta.xr.mrutilitykit.editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/meta.xr.mrutilitykit.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.MultiplayerBlocks.Fusion.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.MultiplayerBlocks.Fusion.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.MultiplayerBlocks.NGO.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.MultiplayerBlocks.Shared.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Meta.XR.MultiplayerBlocks.Shared.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/MetaXrSimulator.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/NuGetForUnity.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.Haptics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.Haptics.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.Interaction.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.Interaction.InterfaceSupport.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.Interaction.OVR.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.Interaction.OVR.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.Interaction.OVR.Samples.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.Interaction.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.Interaction.Samples.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.Interaction.Samples.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.Platform.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.Platform.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.VR.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.VR.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Oculus.VR.Scripts.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Updater.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.EditorCoroutines.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Formats.Fbx.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Formats.Fbx.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.WebRTC.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.WebRTC.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.XR.CoreUtils.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.XR.CoreUtils.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.XR.Interaction.Toolkit.Analytics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.XR.Interaction.Toolkit.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.XR.Interaction.Toolkit.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.XR.Management.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.XR.Management.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.XR.Oculus.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.XR.Oculus.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.SpatialTracking.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.XR.LegacyInputHelpers.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.SpatialTracking.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.XR.LegacyInputHelpers.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/VoiceSDK.Dictation.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/VoiceSDK.Dictation.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/VoiceSDK.Editor.Composer.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/VoiceSDK.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/VoiceSDK.Runtime.Composer.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/VoiceSDK.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/VoiceSDK.Telemetry.ref.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/zh-Hans/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Configuration.Binder.9.0.1/analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hans/Microsoft.Extensions.Logging.Generators.resources.dll"
-analyzer:"Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.1/analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Demos/Scripts/FlyCamera.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Demos/Scripts/RedCube.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Camera/PerfectCullingCamera.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/CustomHandle.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/EBakeRenderMode.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/IO/PerfectCullingBitStreams.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingBakeData.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingBakeGroup.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingBakingBehaviour.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingConstants.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingLogger.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingMath.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingMonoGroup.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingPortalCell.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingRendererTag.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingRenderToggleMode.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingSceneGroup.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingTemp.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingTerrain.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingUtil.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/PerfectCullingVisibilityLayer.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/SamplingProviderBase.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/SamplingProviders/ExcludeBelowColliderArraySamplingProvider.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/SamplingProviders/ExcludeBelowColliderSamplingProvider.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/SamplingProviders/ExcludeFloatingSamplingProvider.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/SamplingProviders/ExcludeInsideCollidersSamplingProvider.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/SamplingProviders/ExcludeTooFarFromNavMeshSamplingProvider.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/TerrainToMeshUtility.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/DefaultActiveSamplingProvider.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/EEmptyCellCullBehaviour.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/IActiveSamplingProvider.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/PerfectCullingAlwaysIncludeVolume.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/PerfectCullingExcludeVolume.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/PerfectCullingVolume.cs"
"Assets/ExternalPackages/Koenigz/Perfect Culling/Scripts/Volume/PerfectCullingVolumeBakeData.cs"
"Assets/Script/Analytics/AnalyticsManager.cs"
"Assets/Script/Analytics/CognetiveManagerWrapper.cs"
"Assets/Script/Analytics/PosthogAdapter.cs"
"Assets/Script/BuildPipeline/MHXPBuildSettings.cs"
"Assets/Script/BuildPipeline/MHXPBuildSettingsEditor.cs"
"Assets/Script/BuildPipeline/MHXPData.cs"
"Assets/Script/CarReferenceCatch.cs"
"Assets/Script/ChatAgent/ChatAgentAbstract.cs"
"Assets/Script/ChatAgent/ChatAgentDebugPanelSetup.cs"
"Assets/Script/ChatAgent/ChatAgentHomeShowcase.cs"
"Assets/Script/ChatAgent/ChatAgentKiosk.cs"
"Assets/Script/ChatAgent/ChatAgentSelection.cs"
"Assets/Script/ChatAgent/Customer.cs"
"Assets/Script/ChatAgent/Gaze/AIInspectable.cs"
"Assets/Script/ChatAgent/Gaze/GazeDetector.cs"
"Assets/Script/ChatAgent/ListenerAgent.cs"
"Assets/Script/ChatAgent/MicRouterExperiment.cs"
"Assets/Script/Config/ConfigDataSerializer.cs"
"Assets/Script/Config/Model/ConfigData.cs"
"Assets/Script/Config/Model/DeviceToRoomMapping.cs"
"Assets/Script/Config/Model/UserInfo.cs"
"Assets/Script/DataController.cs"
"Assets/Script/Flexlayout/Scripts/LightingManager.cs"
"Assets/Script/Flexlayout/Scripts/MeshZoneDivider.cs"
"Assets/Script/Flexlayout/Scripts/RectList.cs"
"Assets/Script/Hostspot/HotspotUIManager.cs"
"Assets/Script/Hotspots/BoundaryGenerator3D.cs"
"Assets/Script/Hotspots/CycleThroughHotspots.cs"
"Assets/Script/Hotspots/EnaableWarning.cs"
"Assets/Script/Hotspots/HierarchyGizmosDirections.cs"
"Assets/Script/Hotspots/HotspotSetManager.cs"
"Assets/Script/Hotspots/HotspotsManager.cs"
"Assets/Script/Hotspots/LevelStartRefrenceCatch.cs"
"Assets/Script/Hotspots/LevelStartShadowRef.cs"
"Assets/Script/Hotspots/RuntimeHotspot.cs"
"Assets/Script/Hotspots/Showcase_Minimap/ShowcaseUIHighlight.cs"
"Assets/Script/Hotspots/SwitchBalcony.cs"
"Assets/Script/Hotspots/VRControllerCheatCode.cs"
"Assets/Script/LevelInIt.cs"
"Assets/Script/LevelsAndZonesSystem/FloorLevelChanges.cs"
"Assets/Script/LevelsAndZonesSystem/LevelBehaviour.cs"
"Assets/Script/LevelsAndZonesSystem/ZoneController.cs"
"Assets/Script/LevelsAndZonesSystem/ZoneDataSerializer.cs"
"Assets/Script/LevelsAndZonesSystem/Zones.cs"
"Assets/Script/LiftRefrenceCatch.cs"
"Assets/Script/LoaderInit.cs"
"Assets/Script/Multiplayer/Scripts/CenterEyeAnchorRefrence.cs"
"Assets/Script/Multiplayer/Scripts/ConnectionManager.cs"
"Assets/Script/Multiplayer/Scripts/ConnectToPhoton.cs"
"Assets/Script/Multiplayer/Scripts/FusionSceneManagement.cs"
"Assets/Script/Multiplayer/Scripts/Logger/Logger.cs"
"Assets/Script/Multiplayer/Scripts/Logger/ToastManager.cs"
"Assets/Script/Multiplayer/Scripts/ModeSelector.cs"
"Assets/Script/Multiplayer/Scripts/NetworkTransformSync.cs"
"Assets/Script/Multiplayer/Scripts/RPCInteractionSource.cs"
"Assets/Script/Multiplayer/Scripts/SceneList.cs"
"Assets/Script/Multiplayer/Scripts/SceneManagerMHXP.cs"
"Assets/Script/Multiplayer/Scripts/SetDeviceMapping.cs"
"Assets/Script/Multiplayer/Scripts/UIManger.cs"
"Assets/Script/Multiplayer/Scripts/UIStateController.cs"
"Assets/Script/Multiplayer/Scripts/VRNetworkObject.cs"
"Assets/Script/Spatial Anchors/AlignMesh.cs"
"Assets/Script/Spatial Anchors/AnchorControllerInstance.cs"
"Assets/Script/Spatial Anchors/ButtonMapperInstance.cs"
"Assets/Script/Spatial Anchors/DontDestroyAnchors.cs"
"Assets/Script/Spatial Anchors/MeshAllignerRefrenceCatch.cs"
"Assets/Script/Spatial Anchors/RefrenceCatch/AnchorRef.cs"
"Assets/Script/Spatial Anchors/RefrenceCatch/AnchorShadowRef.cs"
"Assets/Script/Spatial Anchors/RefrenceCatch/CameraRef.cs"
"Assets/Script/Spatial Anchors/RefrenceCatch/LevelStartRef.cs"
"Assets/Script/Spatial Anchors/RefrenceCatch/MeshParent.cs"
"Assets/Script/Spatial Anchors/RefrenceCatch/VRRigCameraRef.cs"
"Assets/Script/Spatial Anchors/SpatialAnchorCoreBuildingBlockInstance.cs"
"Assets/Script/Spatial Anchors/TestInstantiate.cs"
"Assets/Script/TestData/TestCanvasDataViewer.cs"
"Assets/Script/Tests/TestUIScripts.cs"
"Assets/Script/ToggleElement.cs"
"Assets/Script/ToggleInteriors.cs"
"Assets/Script/UI/Carousel/CarouselDemoSetup.cs"
"Assets/Script/UI/Carousel/CarouselManager.cs"
"Assets/Script/UI/Carousel/CarouselSwipeDetector.cs"
"Assets/Script/UI/Carousel/MediaItem.cs"
"Assets/Script/UI/EyeBlinkSimulator.cs"
"Assets/Script/UI/HotspotNameButtonParentRef.cs"
"Assets/Script/UI/InputFeildClicked.cs"
"Assets/Script/UI/ToggleSwitch.cs"
"Assets/Script/UI/ToggleSwitchGroupManager.cs"
"Assets/Script/UI/VRBUttonParentRef.cs"
"Assets/Script/UI/VRCanvas.cs"
"Assets/Script/UI/VRLogMessage.cs"
"Assets/Script/Utilities/FileOperations.cs"
"Assets/Script/Utilities/InspectorButtonAttribute.cs"
"Assets/Script/Utilities/Interaction.cs"
"Assets/Script/Utilities/MinimapMapper.cs"
"Assets/Script/Utilities/Movement.cs"
"Assets/Script/Utilities/ReuseLightmapOnLODs.cs"
"Assets/Script/Utilities/Serializer.cs"
"Assets/Script/Utilities/SetTextForMinimapButtons.cs"
"Assets/Script/Utilities/StealLightmap.cs"
"Assets/Script/_KioskExperiance/contents/ananda/AnandaKioskContent.cs"
"Assets/Script/_KioskExperiance/contents/ananda/MiniatureSceneManager.cs"
"Assets/Script/_KioskExperiance/contents/ananda/VirtualHomeManager.cs"
"Assets/Script/_KioskExperiance/FaceRecognizer.cs"
"Assets/Script/_KioskExperiance/KioskContentAdapter.cs"
"Assets/Script/_KioskExperiance/KioskControlPanelManager.cs"
"Assets/Script/_KioskExperiance/KioskGenericContentNodeManager.cs"
"Assets/Script/_KioskExperiance/KioskUIManager.cs"
"Assets/Script/_KioskExperiance/XMLContentReader.cs"
"Assets/Script/_TabletMode/AppController.cs"
"Assets/Script/_TabletMode/CameraMovement/CameraMovement.cs"
"Assets/Script/_TabletMode/CameraMovement/CameraTouchAndMouseRotate.cs"
"Assets/Script/_TabletMode/CameraMovement/ChangeCameraProperties.cs"
"Assets/Script/_TabletMode/CameraMovement/OrbitCamera/MapImageController.cs"
"Assets/Script/_TabletMode/CameraMovement/OrbitCamera/TouchOrbitCamera.cs"
"Assets/Script/_TabletMode/CameraMovement/OrbitCamera/ZoomFadeController.cs"
"Assets/Script/_TabletMode/CameraMovement/RuntimeEditorCamera.cs"
"Assets/Script/_TabletMode/DataController.cs"
"Assets/Script/_TabletMode/Map/BillboardLookAt.cs"
"Assets/Script/_TabletMode/Map/CesiumPOIManager.cs"
"Assets/Script/_TabletMode/PlayerHeightController.cs"
"Assets/Script/_TabletMode/SceneLoader.cs"
"Assets/Script/_TabletMode/TapAndSwipeController.cs"
"Assets/Script/_TabletMode/UI/IntroSceneButtonClicks.cs"
"Assets/Script/_TabletMode/UI/UIManagerTabletMode.cs"
"Assets/Script/_TabletMode/UI/UImangerProjectLevel.cs"
"Assets/Script/_TabletMode/Utilities/HotspotDataHolder.cs"
"Assets/Script/_TabletMode/Utilities/OnClickCallback.cs"
"Assets/Scripts/DirectionalLayoutSystem/DirectionalLayoutAnchor.cs"
"Assets/Scripts/DirectionalLayoutSystem/DirectionalLayoutData.cs"
"Assets/Scripts/DirectionalLayoutSystem/DirectionalLayoutItem.cs"
"Assets/Scripts/DirectionalLayoutSystem/DirectionalLayoutLoader.cs"
"Assets/Scripts/DirectionalLayoutSystem/DirectionalLayoutSaver.cs"
"Assets/Scripts/DirectionalLayoutSystem/DirectionalLayoutVolume.cs"
"Assets/Scripts/DirectionalLayoutSystem/LayoutifyTool.cs"
"Assets/Scripts/DirectionalLayoutSystem/Side.cs"
"Assets/Scripts/DirectionalLayoutSystem/SideMapping.cs"
"Assets/Scripts/DirectionalLayoutSystem/SideOffset.cs"
"Assets/Transition_tessellation/Scripts/FadeScript.cs"
"Assets/Transition_tessellation/Scripts/RevealTessellation.cs"
"Assets/Transition_tessellation/Scripts/ToggleManager.cs"
"Assets/Transition_tessellation/Scripts/TrasitionMesh.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize-
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.UnityAdditionalFile.txt"