Base path: '/Applications/Unity/Hub/Editor/6000.1.4f1/Unity.app/Contents', plugins path '/Applications/Unity/Hub/Editor/6000.1.4f1/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileSnippet
  insize=1713 file=Packages/com.unity.render-pipelines.universal/Shaders/SimpleLit.shader name=Universal Render Pipeline/Simple Lit pass=DepthOnly ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING uKW= dKW=LOD_FADE_CROSSFADE DOTS_INSTANCING_ON INSTANCING_ON _ALPHATEST_ON UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=3 type=Vertex platform=metal reqs=1 mask=6 start=286 ok=1 outsize=5360

Cmd: compileSnippet
  insize=87347 file=Packages/com.unity.render-pipelines.universal/Shaders/Nature/SpeedTree8_PBRLit.shadergraph name=Universal Render Pipeline/Nature/SpeedTree8_PBRLit pass=Universal Forward ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING uKW=LIGHTMAP_ON EVALUATE_SH_VERTEX LOD_FADE_CROSSFADE _WINDQUALITY_PALM dKW=DYNAMICLIGHTMAP_ON DIRLIGHTMAP_COMBINED USE_LEGACY_LIGHTMAPS LIGHTMAP_BICUBIC_SAMPLING _MAIN_LIGHT_SHADOWS _MAIN_LIGHT_SHADOWS_CASCADE _MAIN_LIGHT_SHADOWS_SCREEN _ADDITIONAL_LIGHTS_VERTEX _ADDITIONAL_LIGHTS LIGHTMAP_SHADOW_MIXING SHADOWS_SHADOWMASK _CLUSTER_LIGHT_LOOP EVALUATE_SH_MIXED DOTS_INSTANCING_ON PROBE_VOLUMES_L1 PROBE_VOLUMES_L2 INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 _WINDQUALITY_NONE _WINDQUALITY_FASTEST _WINDQUALITY_FAST _WINDQUALITY_BETTER _WINDQUALITY_BEST EFFECT_BILLBOARD UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=3 type=Vertex platform=metal reqs=1 mask=6 start=63 ok=1 outsize=13381

